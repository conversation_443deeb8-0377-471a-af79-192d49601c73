-- StarRocks数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS starrocks_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE starrocks_db;

-- 用户表
CREATE TABLE IF NOT EXISTS sys_user (
  user_id           BIGINT(20)      NOT NULL AUTO_INCREMENT    COMMENT '用户ID',
  dept_id           BIGINT(20)      DEFAULT NULL               COMMENT '部门ID',
  login_name        VARCHAR(30)     NOT NULL                   COMMENT '登录账号',
  user_name         VARCHAR(30)     NOT NULL                   COMMENT '用户昵称',
  user_type         VARCHAR(2)      DEFAULT '00'               COMMENT '用户类型（00系统用户 01注册用户）',
  email             VARCHAR(50)     DEFAULT ''                 COMMENT '用户邮箱',
  phonenumber       VARCHAR(11)     DEFAULT ''                 COMMENT '手机号码',
  sex               CHAR(1)         DEFAULT '0'                COMMENT '用户性别（0男 1女 2未知）',
  avatar            VARCHAR(100)    DEFAULT ''                 COMMENT '头像路径',
  password          VARCHAR(50)     DEFAULT ''                 COMMENT '密码',
  salt              VARCHAR(20)     DEFAULT ''                 COMMENT '盐加密',
  status            CHAR(1)         DEFAULT '0'                COMMENT '帐号状态（0正常 1停用）',
  del_flag          CHAR(1)         DEFAULT '0'                COMMENT '删除标志（0代表存在 1代表删除）',
  login_ip          VARCHAR(128)    DEFAULT ''                 COMMENT '最后登录IP',
  login_date        DATETIME                                   COMMENT '最后登录时间',
  create_by         VARCHAR(64)     DEFAULT ''                 COMMENT '创建者',
  create_time       DATETIME                                   COMMENT '创建时间',
  update_by         VARCHAR(64)     DEFAULT ''                 COMMENT '更新者',
  update_time       DATETIME                                   COMMENT '更新时间',
  remark            VARCHAR(500)    DEFAULT NULL               COMMENT '备注',
  PRIMARY KEY (user_id)
) ENGINE=OLAP 
PRIMARY KEY(user_id)
DISTRIBUTED BY HASH(user_id) BUCKETS 10
PROPERTIES("replication_num" = "1");

-- 角色表
CREATE TABLE IF NOT EXISTS sys_role (
  role_id           BIGINT(20)      NOT NULL AUTO_INCREMENT    COMMENT '角色ID',
  role_name         VARCHAR(30)     NOT NULL                   COMMENT '角色名称',
  role_key          VARCHAR(100)    NOT NULL                   COMMENT '角色权限字符串',
  role_sort         INT(4)          NOT NULL                   COMMENT '显示顺序',
  data_scope        CHAR(1)         DEFAULT '1'                COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  status            CHAR(1)         NOT NULL                   COMMENT '角色状态（0正常 1停用）',
  del_flag          CHAR(1)         DEFAULT '0'                COMMENT '删除标志（0代表存在 1代表删除）',
  create_by         VARCHAR(64)     DEFAULT ''                 COMMENT '创建者',
  create_time       DATETIME                                   COMMENT '创建时间',
  update_by         VARCHAR(64)     DEFAULT ''                 COMMENT '更新者',
  update_time       DATETIME                                   COMMENT '更新时间',
  remark            VARCHAR(500)    DEFAULT NULL               COMMENT '备注',
  PRIMARY KEY (role_id)
) ENGINE=OLAP 
PRIMARY KEY(role_id)
DISTRIBUTED BY HASH(role_id) BUCKETS 10
PROPERTIES("replication_num" = "1");

-- 菜单权限表
CREATE TABLE IF NOT EXISTS sys_menu (
  menu_id           BIGINT(20)      NOT NULL AUTO_INCREMENT    COMMENT '菜单ID',
  menu_name         VARCHAR(50)     NOT NULL                   COMMENT '菜单名称',
  parent_id         BIGINT(20)      DEFAULT 0                  COMMENT '父菜单ID',
  order_num         INT(4)          DEFAULT 0                  COMMENT '显示顺序',
  url               VARCHAR(200)    DEFAULT '#'                COMMENT '请求地址',
  target            VARCHAR(20)     DEFAULT ''                 COMMENT '打开方式（menuItem页签 menuBlank新窗口）',
  menu_type         CHAR(1)         DEFAULT ''                 COMMENT '菜单类型（M目录 C菜单 F按钮）',
  visible           CHAR(1)         DEFAULT '0'                COMMENT '菜单状态（0显示 1隐藏）',
  perms             VARCHAR(100)    DEFAULT NULL               COMMENT '权限标识',
  icon              VARCHAR(100)    DEFAULT '#'                COMMENT '菜单图标',
  create_by         VARCHAR(64)     DEFAULT ''                 COMMENT '创建者',
  create_time       DATETIME                                   COMMENT '创建时间',
  update_by         VARCHAR(64)     DEFAULT ''                 COMMENT '更新者',
  update_time       DATETIME                                   COMMENT '更新时间',
  remark            VARCHAR(500)    DEFAULT ''                 COMMENT '备注',
  PRIMARY KEY (menu_id)
) ENGINE=OLAP 
PRIMARY KEY(menu_id)
DISTRIBUTED BY HASH(menu_id) BUCKETS 10
PROPERTIES("replication_num" = "1");

-- 部门表
CREATE TABLE IF NOT EXISTS sys_dept (
  dept_id           BIGINT(20)      NOT NULL AUTO_INCREMENT    COMMENT '部门id',
  parent_id         BIGINT(20)      DEFAULT 0                  COMMENT '父部门id',
  ancestors         VARCHAR(50)     DEFAULT ''                 COMMENT '祖级列表',
  dept_name         VARCHAR(30)     NOT NULL                   COMMENT '部门名称',
  order_num         INT(4)          DEFAULT 0                  COMMENT '显示顺序',
  leader            VARCHAR(20)     DEFAULT NULL               COMMENT '负责人',
  phone             VARCHAR(11)     DEFAULT NULL               COMMENT '联系电话',
  email             VARCHAR(50)     DEFAULT NULL               COMMENT '邮箱',
  status            CHAR(1)         DEFAULT '0'                COMMENT '部门状态（0正常 1停用）',
  del_flag          CHAR(1)         DEFAULT '0'                COMMENT '删除标志（0代表存在 1代表删除）',
  create_by         VARCHAR(64)     DEFAULT ''                 COMMENT '创建者',
  create_time       DATETIME                                   COMMENT '创建时间',
  update_by         VARCHAR(64)     DEFAULT ''                 COMMENT '更新者',
  update_time       DATETIME                                   COMMENT '更新时间',
  PRIMARY KEY (dept_id)
) ENGINE=OLAP 
PRIMARY KEY(dept_id)
DISTRIBUTED BY HASH(dept_id) BUCKETS 10
PROPERTIES("replication_num" = "1");

-- 岗位表
CREATE TABLE IF NOT EXISTS sys_post (
  post_id           BIGINT(20)      NOT NULL AUTO_INCREMENT    COMMENT '岗位ID',
  post_code         VARCHAR(64)     NOT NULL                   COMMENT '岗位编码',
  post_name         VARCHAR(50)     NOT NULL                   COMMENT '岗位名称',
  post_sort         INT(4)          NOT NULL                   COMMENT '显示顺序',
  status            CHAR(1)         NOT NULL                   COMMENT '状态（0正常 1停用）',
  create_by         VARCHAR(64)     DEFAULT ''                 COMMENT '创建者',
  create_time       DATETIME                                   COMMENT '创建时间',
  update_by         VARCHAR(64)     DEFAULT ''                 COMMENT '更新者',
  update_time       DATETIME                                   COMMENT '更新时间',
  remark            VARCHAR(500)    DEFAULT NULL               COMMENT '备注',
  PRIMARY KEY (post_id)
) ENGINE=OLAP 
PRIMARY KEY(post_id)
DISTRIBUTED BY HASH(post_id) BUCKETS 10
PROPERTIES("replication_num" = "1");

-- 操作日志表
CREATE TABLE IF NOT EXISTS sys_oper_log (
  oper_id           BIGINT(20)      NOT NULL AUTO_INCREMENT    COMMENT '日志主键',
  title             VARCHAR(50)     DEFAULT ''                 COMMENT '模块标题',
  business_type     INT(2)          DEFAULT 0                  COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  method            VARCHAR(100)    DEFAULT ''                 COMMENT '方法名称',
  request_method    VARCHAR(10)     DEFAULT ''                 COMMENT '请求方式',
  operator_type     INT(1)          DEFAULT 0                  COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  oper_name         VARCHAR(50)     DEFAULT ''                 COMMENT '操作人员',
  dept_name         VARCHAR(50)     DEFAULT ''                 COMMENT '部门名称',
  oper_url          VARCHAR(255)    DEFAULT ''                 COMMENT '请求URL',
  oper_ip           VARCHAR(128)    DEFAULT ''                 COMMENT '主机地址',
  oper_location     VARCHAR(255)    DEFAULT ''                 COMMENT '操作地点',
  oper_param        VARCHAR(2000)   DEFAULT ''                 COMMENT '请求参数',
  json_result       VARCHAR(2000)   DEFAULT ''                 COMMENT '返回参数',
  status            INT(1)          DEFAULT 0                  COMMENT '操作状态（0正常 1异常）',
  error_msg         VARCHAR(2000)   DEFAULT ''                 COMMENT '错误消息',
  oper_time         DATETIME                                   COMMENT '操作时间',
  PRIMARY KEY (oper_id)
) ENGINE=OLAP 
PRIMARY KEY(oper_id)
DISTRIBUTED BY HASH(oper_id) BUCKETS 10
PROPERTIES("replication_num" = "1");

-- 字典类型表
CREATE TABLE IF NOT EXISTS sys_dict_type (
  dict_id          BIGINT(20)      NOT NULL AUTO_INCREMENT    COMMENT '字典主键',
  dict_name        VARCHAR(100)    DEFAULT ''                 COMMENT '字典名称',
  dict_type        VARCHAR(100)    DEFAULT ''                 COMMENT '字典类型',
  status           CHAR(1)         DEFAULT '0'                COMMENT '状态（0正常 1停用）',
  create_by        VARCHAR(64)     DEFAULT ''                 COMMENT '创建者',
  create_time      DATETIME                                   COMMENT '创建时间',
  update_by        VARCHAR(64)     DEFAULT ''                 COMMENT '更新者',
  update_time      DATETIME                                   COMMENT '更新时间',
  remark           VARCHAR(500)    DEFAULT NULL               COMMENT '备注',
  PRIMARY KEY (dict_id)
) ENGINE=OLAP 
PRIMARY KEY(dict_id)
DISTRIBUTED BY HASH(dict_id) BUCKETS 10
PROPERTIES("replication_num" = "1");

-- 字典数据表
CREATE TABLE IF NOT EXISTS sys_dict_data (
  dict_code        BIGINT(20)      NOT NULL AUTO_INCREMENT    COMMENT '字典编码',
  dict_sort        INT(4)          DEFAULT 0                  COMMENT '字典排序',
  dict_label       VARCHAR(100)    DEFAULT ''                 COMMENT '字典标签',
  dict_value       VARCHAR(100)    DEFAULT ''                 COMMENT '字典键值',
  dict_type        VARCHAR(100)    DEFAULT ''                 COMMENT '字典类型',
  css_class        VARCHAR(100)    DEFAULT NULL               COMMENT '样式属性（其他样式扩展）',
  list_class       VARCHAR(50)     DEFAULT NULL               COMMENT '表格回显样式',
  is_default       CHAR(1)         DEFAULT 'N'                COMMENT '是否默认（Y是 N否）',
  status           CHAR(1)         DEFAULT '0'                COMMENT '状态（0正常 1停用）',
  create_by        VARCHAR(64)     DEFAULT ''                 COMMENT '创建者',
  create_time      DATETIME                                   COMMENT '创建时间',
  update_by        VARCHAR(64)     DEFAULT ''                 COMMENT '更新者',
  update_time      DATETIME                                   COMMENT '更新时间',
  remark           VARCHAR(500)    DEFAULT NULL               COMMENT '备注',
  PRIMARY KEY (dict_code)
) ENGINE=OLAP 
PRIMARY KEY(dict_code)
DISTRIBUTED BY HASH(dict_code) BUCKETS 10
PROPERTIES("replication_num" = "1");