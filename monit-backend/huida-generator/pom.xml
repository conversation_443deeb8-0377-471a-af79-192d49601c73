<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.huida</groupId>
        <artifactId>huida-monitor-platform</artifactId>
        <version>1.0.0</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>huida-generator</artifactId>
    <packaging>jar</packaging>

    <name>huida-generator</name>
    <description>监控平台代码生成模块</description>

    <dependencies>
        <!-- SpringBoot Web容器 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- SpringBoot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- velocity代码生成使用模板 -->
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId>
        </dependency>

        <!-- 通用工具 -->
        <dependency>
            <groupId>com.huida</groupId>
            <artifactId>huida-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- 系统模块 -->
        <dependency>
            <groupId>com.huida</groupId>
            <artifactId>huida-system</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>
</project>