#!/bin/bash

# 汇易达保单监控系统 - 快速启动脚本
# 版本: v3.3.11

# 配置变量
BACKEND_JAR="ruoyi-admin.jar"
FRONTEND_DIR="ruoyi-ui"
BACKEND_PORT=8080
FRONTEND_PORT=80

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 检查端口是否被占用
port_in_use() {
    local port=$1
    if command_exists lsof; then
        lsof -ti:$port >/dev/null 2>&1
    elif command_exists netstat; then
        netstat -tuln | grep ":$port " >/dev/null 2>&1
    else
        return 1
    fi
}

# 等待服务启动
wait_for_service() {
    local port=$1
    local service_name=$2
    local max_wait=30
    local count=0
    
    print_message $BLUE "等待 $service_name 启动..."
    while [ $count -lt $max_wait ]; do
        if port_in_use $port; then
            print_message $GREEN "✅ $service_name 启动成功！"
            return 0
        fi
        sleep 1
        count=$((count + 1))
        printf "."
    done
    echo
    print_message $RED "❌ $service_name 启动超时"
    return 1
}

echo
echo "========================================"
echo "  汇易达保单监控系统 - 快速启动"
echo "========================================"
echo

print_message $BLUE "正在检查环境..."

# 检查Java环境
if ! command_exists java; then
    print_message $RED "❌ Java 未安装或未配置到PATH"
    print_message $YELLOW "请安装 Java 8 或更高版本"
    exit 1
fi
print_message $GREEN "✅ Java 环境正常"

# 检查Node.js环境
if ! command_exists node; then
    print_message $RED "❌ Node.js 未安装或未配置到PATH"
    print_message $YELLOW "请安装 Node.js 14 或更高版本"
    exit 1
fi
print_message $GREEN "✅ Node.js 环境正常"

# 检查npm
if ! command_exists npm; then
    print_message $RED "❌ npm 未安装"
    print_message $YELLOW "请安装 npm"
    exit 1
fi
print_message $GREEN "✅ npm 环境正常"

# 检查Maven环境
MAVEN_AVAILABLE=false
if command_exists mvn; then
    print_message $GREEN "✅ Maven 环境正常"
    MAVEN_AVAILABLE=true
else
    print_message $YELLOW "⚠️  Maven 未安装，将跳过构建步骤"
fi

echo
print_message $BLUE "正在启动服务..."

# 检查并构建后端
if [ ! -f "$BACKEND_JAR" ]; then
    if [ "$MAVEN_AVAILABLE" = true ]; then
        print_message $BLUE "🔨 后端JAR文件不存在，正在构建..."
        mvn clean package -Dmaven.test.skip=true -q
        if [ $? -ne 0 ]; then
            print_message $RED "❌ 后端构建失败"
            exit 1
        fi
        if [ -f "ruoyi-admin/target/ruoyi-admin.jar" ]; then
            cp ruoyi-admin/target/ruoyi-admin.jar .
            print_message $GREEN "✅ 后端构建完成"
        else
            print_message $RED "❌ 构建失败，未找到JAR文件"
            exit 1
        fi
    else
        print_message $RED "❌ 后端JAR文件不存在且Maven不可用"
        print_message $YELLOW "请手动构建项目或安装Maven"
        exit 1
    fi
fi

# 启动后端服务
print_message $BLUE "🚀 启动后端服务..."
backend_pid=$(ps -ef | grep java | grep $BACKEND_JAR | grep -v grep | awk '{print $2}')
if [ -n "$backend_pid" ]; then
    print_message $YELLOW "⚠️  后端服务已在运行 (PID: $backend_pid)"
else
    nohup java -Dname=$BACKEND_JAR -Duser.timezone=Asia/Shanghai -Xms512m -Xmx1024m -jar $BACKEND_JAR > /dev/null 2>&1 &
    wait_for_service $BACKEND_PORT "后端服务"
fi

# 启动前端服务
print_message $BLUE "🚀 启动前端服务..."
if [ ! -d "$FRONTEND_DIR" ]; then
    print_message $RED "❌ 前端目录不存在: $FRONTEND_DIR"
    exit 1
fi

cd $FRONTEND_DIR

# 检查依赖
if [ ! -d "node_modules" ]; then
    print_message $BLUE "📦 安装前端依赖..."
    npm install
    if [ $? -ne 0 ]; then
        print_message $RED "❌ 依赖安装失败"
        cd ..
        exit 1
    fi
    print_message $GREEN "✅ 依赖安装完成"
fi

# 检查前端是否已运行
if port_in_use $FRONTEND_PORT; then
    print_message $YELLOW "⚠️  前端服务可能已在运行 (端口$FRONTEND_PORT被占用)"
else
    print_message $BLUE "🚀 启动前端开发服务器..."
    nohup npm run dev > /dev/null 2>&1 &
    wait_for_service $FRONTEND_PORT "前端服务"
fi

cd ..

echo
echo "========================================"
echo "  启动完成！"
echo "========================================"
echo
print_message $GREEN "🌐 前端访问地址: http://localhost:$FRONTEND_PORT"
print_message $GREEN "🔧 后端访问地址: http://localhost:$BACKEND_PORT"
print_message $GREEN "📚 API文档地址:  http://localhost:$BACKEND_PORT/swagger-ui.html"
print_message $GREEN "📊 数据库监控:   http://localhost:$BACKEND_PORT/druid/"
echo
print_message $BLUE "默认登录账号:"
print_message $BLUE "  用户名: admin"
print_message $BLUE "  密码:   123456"
echo
print_message $YELLOW "💡 提示:"
print_message $YELLOW "  - 首次启动可能需要等待1-2分钟"
print_message $YELLOW "  - 如遇到问题，请检查端口是否被占用"
print_message $YELLOW "  - 可使用 ./start-all.sh 进行更多操作"
echo

# 尝试打开浏览器
if command_exists xdg-open; then
    xdg-open "http://localhost:$FRONTEND_PORT" >/dev/null 2>&1 &
elif command_exists open; then
    open "http://localhost:$FRONTEND_PORT" >/dev/null 2>&1 &
fi

print_message $GREEN "🎉 系统启动完成，请在浏览器中访问上述地址！"
