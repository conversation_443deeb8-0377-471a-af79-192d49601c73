# 汇易达保单监控系统

<p align="center">
    <img alt="logo" src="images/img001.png" width="200">
</p>
<h1 align="center" style="margin: 30px 0 30px; font-weight: bold;">汇易达保单监控系统 v3.3.11</h1>
<h4 align="center">基于若依框架 + StarRocks的保单监控管理平台</h4>

## 📋 项目简介

汇易达保单监控系统是一个基于若依框架开发的保单监控管理平台，主要用于监控保单的各种状态和数据处理流程。系统支持StarRocks 3.3.11数据库，具备高性能的数据处理和分析能力。

### 🎯 核心功能
- **保单监控明细管理** - 实时监控保单状态变化
- **监控汇总结果统计** - 多维度数据统计分析  
- **资产文件明细管理** - 资产文件处理和管理
- **配置信息管理** - 系统配置参数管理
- **FTP信息管理** - FTP连接配置管理
- **批处理任务配置** - 定时任务调度管理

## 🛠 技术栈

| 技术 | 版本 | 说明 |
|------|------|------|
| **后端框架** | Spring Boot 2.5+ | 主框架 |
| **安全框架** | Spring Security | 权限控制 |
| **持久层** | MyBatis | ORM框架 |
| **数据库** | StarRocks 3.3.11 | 分析型数据库 |
| **缓存** | Redis 5.0+ | 分布式缓存 |
| **前端框架** | Vue.js 2.6+ | 前端框架 |
| **UI组件** | Element UI | UI组件库 |
| **构建工具** | Maven + npm | 项目构建 |

## 🚀 快速启动

### 🔧 环境要求
- **Java**: JDK 8+
- **Node.js**: 14.x+
- **Maven**: 3.6+
- **StarRocks**: 3.3.11+
- **Redis**: 5.0+

### ⚡ 一键启动（推荐）

**Windows 环境:**
```bash
# 1. 环境检查
check-env.bat

# 2. 快速启动
quick-start.bat
```

**Linux 环境:**
```bash
# 1. 设置权限
chmod +x *.sh

# 2. 环境检查  
./check-env.sh

# 3. 快速启动
./quick-start.sh
```

### 📝 详细启动步骤

1. **数据库初始化**
   ```sql
   -- 创建表结构
   source sql/starrocks_init_ddl_v3311.sql;
   
   -- 插入初始数据
   source sql/starrocks_init_dml_v3311.sql;
   ```

2. **配置文件修改**
   - 数据库配置: `ruoyi-admin/src/main/resources/application-druid.yml`
   - Redis配置: `ruoyi-admin/src/main/resources/application.yml`

3. **启动服务**
   ```bash
   # Windows
   start-all.bat
   
   # Linux  
   ./start-all.sh start
   ```

## 🌐 访问地址

| 服务 | 地址 | 账号 |
|------|------|------|
| **前端系统** | http://localhost | admin / 123456 |
| **后端API** | http://localhost:8080 | - |
| **API文档** | http://localhost:8080/swagger-ui.html | - |
| **数据库监控** | http://localhost:8080/druid/ | ruoyi / 123456 |

## 📁 项目结构

```
huida-monitor-platform/
├── 📁 ruoyi-admin/          # 后端主模块
├── 📁 ruoyi-common/         # 公共模块  
├── 📁 ruoyi-framework/      # 框架模块
├── 📁 ruoyi-generator/      # 代码生成模块
├── 📁 ruoyi-quartz/         # 定时任务模块
├── 📁 ruoyi-system/         # 系统模块
├── 📁 ruoyi-ui/             # 前端模块
├── 📁 sql/                  # 数据库脚本
│   ├── 📄 starrocks_init_ddl_v3311.sql    # DDL脚本
│   ├── 📄 starrocks_init_dml_v3311.sql    # DML脚本
│   └── 📄 README_v3311_update.md          # 更新说明
├── 📁 doc/                  # 文档目录
├── 📁 bin/                  # 构建脚本
├── 📄 start-all.bat         # Windows启动脚本
├── 📄 start-all.sh          # Linux启动脚本
├── 📄 quick-start.bat       # Windows快速启动
├── 📄 quick-start.sh        # Linux快速启动
├── 📄 check-env.bat         # Windows环境检查
├── 📄 check-env.sh          # Linux环境检查
└── 📄 启动说明.md           # 详细启动说明
```

## 🎮 启动脚本说明

| 脚本 | 平台 | 功能 |
|------|------|------|
| `quick-start.*` | 全平台 | 🚀 一键启动，自动检查环境和构建 |
| `start-all.*` | 全平台 | 🎛️ 完整管理，提供启动/停止/重启等操作 |
| `check-env.*` | 全平台 | 🔍 环境检查，验证所有必需环境 |
| `ry.*` | 全平台 | 📦 原始若依后端启动脚本 |

### 🎯 推荐使用方式
- **新手用户**: 使用 `quick-start.*` 一键启动
- **开发人员**: 使用 `start-all.*` 进行服务管理
- **运维人员**: 使用 `check-env.*` 进行环境检查

## 🔧 开发指南

### 🏗️ 构建项目
```bash
# 后端构建
mvn clean package -Dmaven.test.skip=true

# 前端构建
cd ruoyi-ui
npm install
npm run build:prod
```

### 🐛 调试模式
```bash
# 后端调试
mvn spring-boot:run

# 前端调试
cd ruoyi-ui
npm run dev
```

### 📊 代码生成
1. 访问: http://localhost:8080
2. 进入: 系统工具 -> 代码生成
3. 选择表进行代码生成

## 🗄️ 数据库特性

### StarRocks 3.3.11 支持
- ✅ AUTO_INCREMENT 自增主键
- ✅ OLAP 分析型存储引擎
- ✅ 分布式架构支持
- ✅ 高性能数据查询
- ✅ 实时数据更新

### 表结构特点
- 所有系统表支持自增ID
- 优化的分桶策略
- 合理的副本配置
- 兼容若依框架标准

## 📈 性能优化

### 数据库优化
- 使用DUPLICATE KEY模型
- 合理设置分桶数量
- 配置适当的副本数
- 优化查询索引

### 应用优化
- Redis缓存加速
- 连接池优化配置
- JVM参数调优
- 静态资源压缩

## ❓ 常见问题

### 🔧 环境问题
- **端口占用**: 检查8080和80端口
- **Java版本**: 确保JDK 8+
- **Node.js版本**: 确保14.x+
- **Maven配置**: 检查MAVEN_HOME

### 🗄️ 数据库问题
- **连接失败**: 检查StarRocks服务状态
- **权限错误**: 确认数据库用户权限
- **初始化失败**: 检查SQL脚本执行顺序

### 🚀 启动问题
- **构建失败**: 清理Maven缓存重新构建
- **依赖安装失败**: 清理npm缓存重新安装
- **服务无法访问**: 检查防火墙和网络配置

## 📞 技术支持

### 📚 文档资源
- [启动说明](启动说明.md) - 详细启动指南
- [数据库更新说明](sql/README_v3311_update.md) - 数据库脚本说明
- [若依官方文档](http://doc.ruoyi.vip/) - 框架文档

### 🛠️ 问题排查
1. 查看日志: `logs/` 目录
2. 检查配置: 数据库和Redis连接
3. 验证环境: 运行环境检查脚本
4. 联系支持: 技术支持团队

---

<p align="center">
    <b>🎉 感谢使用汇易达保单监控系统！</b>
</p>
