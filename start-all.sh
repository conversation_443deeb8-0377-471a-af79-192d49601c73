#!/bin/bash

# 汇易达保单监控系统启动脚本
# 版本: v3.3.11
# 日期: 2025-08-19

# 配置变量
BACKEND_JAR="ruoyi-admin.jar"
FRONTEND_DIR="ruoyi-ui"
BACKEND_PORT=8080
FRONTEND_PORT=80
JVM_OPTS="-Dname=$BACKEND_JAR -Duser.timezone=Asia/Shanghai -Xms512m -Xmx1024m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError -XX:+PrintGCDateStamps -XX:+PrintGCDetails -XX:NewRatio=1 -XX:SurvivorRatio=30 -XX:+UseParallelGC -XX:+UseParallelOldGC"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 打印标题
print_title() {
    echo
    echo "========================================"
    echo "  汇易达保单监控系统启动脚本"
    echo "  版本: v3.3.11"
    echo "  日期: 2025-08-19"
    echo "========================================"
    echo
}

# 显示菜单
show_menu() {
    echo "[1] 启动后端服务 (端口: $BACKEND_PORT)"
    echo "[2] 启动前端服务 (端口: $FRONTEND_PORT)"
    echo "[3] 启动全部服务 (前端 + 后端)"
    echo "[4] 停止后端服务"
    echo "[5] 停止前端服务"
    echo "[6] 停止全部服务"
    echo "[7] 重启全部服务"
    echo "[8] 查看服务状态"
    echo "[9] 构建后端项目"
    echo "[0] 退出"
    echo
}

# 检查后端服务状态
check_backend_status() {
    local pid=$(ps -ef | grep java | grep $BACKEND_JAR | grep -v grep | awk '{print $2}')
    if [ -n "$pid" ]; then
        return 0  # 运行中
    else
        return 1  # 未运行
    fi
}

# 检查前端服务状态
check_frontend_status() {
    local pid=$(lsof -ti:$FRONTEND_PORT 2>/dev/null)
    if [ -n "$pid" ]; then
        return 0  # 运行中
    else
        return 1  # 未运行
    fi
}

# 启动后端服务
start_backend() {
    print_message $BLUE "启动后端服务..."
    
    if [ ! -f "$BACKEND_JAR" ]; then
        print_message $RED "错误: $BACKEND_JAR 文件不存在！"
        print_message $YELLOW "请先执行构建操作 [9]"
        return 1
    fi
    
    if check_backend_status; then
        local pid=$(ps -ef | grep java | grep $BACKEND_JAR | grep -v grep | awk '{print $2}')
        print_message $YELLOW "后端服务已在运行 (PID: $pid)"
        print_message $GREEN "访问地址: http://localhost:$BACKEND_PORT"
        return 0
    fi
    
    print_message $BLUE "正在启动后端服务..."
    nohup java $JVM_OPTS -jar $BACKEND_JAR > /dev/null 2>&1 &
    
    sleep 5
    if check_backend_status; then
        print_message $GREEN "后端服务启动成功！"
        print_message $GREEN "访问地址: http://localhost:$BACKEND_PORT"
        print_message $GREEN "Swagger文档: http://localhost:$BACKEND_PORT/swagger-ui.html"
        print_message $GREEN "Druid监控: http://localhost:$BACKEND_PORT/druid/"
    else
        print_message $RED "后端服务启动失败！"
        return 1
    fi
}

# 启动前端服务
start_frontend() {
    print_message $BLUE "启动前端服务..."
    
    if [ ! -d "$FRONTEND_DIR" ]; then
        print_message $RED "错误: $FRONTEND_DIR 目录不存在！"
        return 1
    fi
    
    if check_frontend_status; then
        local pid=$(lsof -ti:$FRONTEND_PORT)
        print_message $YELLOW "前端服务已在运行 (PID: $pid)"
        print_message $GREEN "访问地址: http://localhost:$FRONTEND_PORT"
        return 0
    fi
    
    cd $FRONTEND_DIR
    
    # 检查依赖
    if [ ! -d "node_modules" ]; then
        print_message $BLUE "正在安装依赖..."
        npm install
        if [ $? -ne 0 ]; then
            print_message $RED "依赖安装失败！"
            cd ..
            return 1
        fi
    fi
    
    print_message $BLUE "正在启动前端开发服务器..."
    nohup npm run dev > /dev/null 2>&1 &
    
    cd ..
    sleep 3
    
    if check_frontend_status; then
        print_message $GREEN "前端服务启动成功！"
        print_message $GREEN "访问地址: http://localhost:$FRONTEND_PORT"
    else
        print_message $RED "前端服务启动失败！"
        return 1
    fi
}

# 启动全部服务
start_all() {
    print_message $BLUE "启动全部服务..."
    start_backend
    sleep 8
    start_frontend
    echo
    print_message $GREEN "全部服务启动完成！"
    print_message $GREEN "前端访问地址: http://localhost:$FRONTEND_PORT"
    print_message $GREEN "后端访问地址: http://localhost:$BACKEND_PORT"
}

# 停止后端服务
stop_backend() {
    print_message $BLUE "停止后端服务..."
    
    local pid=$(ps -ef | grep java | grep $BACKEND_JAR | grep -v grep | awk '{print $2}')
    if [ -n "$pid" ]; then
        print_message $BLUE "正在停止后端服务 (PID: $pid)"
        kill -TERM $pid
        
        # 等待进程结束
        local count=0
        while [ $count -lt 10 ]; do
            if ! check_backend_status; then
                break
            fi
            sleep 1
            count=$((count + 1))
        done
        
        # 如果还没结束，强制杀死
        if check_backend_status; then
            kill -9 $pid
        fi
        
        print_message $GREEN "后端服务已停止"
    else
        print_message $YELLOW "后端服务未运行"
    fi
}

# 停止前端服务
stop_frontend() {
    print_message $BLUE "停止前端服务..."
    
    local pid=$(lsof -ti:$FRONTEND_PORT 2>/dev/null)
    if [ -n "$pid" ]; then
        print_message $BLUE "正在停止前端服务 (PID: $pid)"
        kill -TERM $pid 2>/dev/null
        sleep 2
        
        # 检查是否还在运行，如果是则强制杀死
        if [ -n "$(lsof -ti:$FRONTEND_PORT 2>/dev/null)" ]; then
            kill -9 $pid 2>/dev/null
        fi
        
        print_message $GREEN "前端服务已停止"
    else
        print_message $YELLOW "前端服务未运行"
    fi
}

# 停止全部服务
stop_all() {
    print_message $BLUE "停止全部服务..."
    stop_backend
    stop_frontend
    print_message $GREEN "全部服务已停止"
}

# 重启全部服务
restart_all() {
    print_message $BLUE "重启全部服务..."
    stop_all
    sleep 3
    start_all
    print_message $GREEN "全部服务重启完成！"
}

# 查看服务状态
show_status() {
    print_message $BLUE "服务状态检查..."
    echo
    
    echo "后端服务状态:"
    if check_backend_status; then
        local pid=$(ps -ef | grep java | grep $BACKEND_JAR | grep -v grep | awk '{print $2}')
        print_message $GREEN "  ✓ 运行中 (PID: $pid)"
        print_message $GREEN "  ✓ 访问地址: http://localhost:$BACKEND_PORT"
    else
        print_message $RED "  ✗ 未运行"
    fi
    
    echo
    echo "前端服务状态:"
    if check_frontend_status; then
        local pid=$(lsof -ti:$FRONTEND_PORT 2>/dev/null)
        print_message $GREEN "  ✓ 运行中 (PID: $pid)"
        print_message $GREEN "  ✓ 访问地址: http://localhost:$FRONTEND_PORT"
    else
        print_message $RED "  ✗ 未运行"
    fi
    echo
}

# 构建后端项目
build_backend() {
    print_message $BLUE "构建后端项目..."
    print_message $BLUE "正在执行 Maven 构建..."
    
    mvn clean package -Dmaven.test.skip=true
    
    if [ $? -eq 0 ]; then
        if [ -f "ruoyi-admin/target/ruoyi-admin.jar" ]; then
            cp ruoyi-admin/target/ruoyi-admin.jar .
            print_message $GREEN "构建成功！JAR文件已复制到根目录"
        else
            print_message $RED "构建失败！未找到JAR文件"
        fi
    else
        print_message $RED "构建失败！"
    fi
}

# 主函数
main() {
    print_title
    
    if [ $# -eq 0 ]; then
        # 交互模式
        show_menu
        read -p "请选择操作 (0-9): " choice
        
        case $choice in
            1) start_backend ;;
            2) start_frontend ;;
            3) start_all ;;
            4) stop_backend ;;
            5) stop_frontend ;;
            6) stop_all ;;
            7) restart_all ;;
            8) show_status ;;
            9) build_backend ;;
            0) print_message $GREEN "感谢使用汇易达保单监控系统！"; exit 0 ;;
            *) print_message $RED "无效选择，请重新运行脚本" ;;
        esac
    else
        # 命令行模式
        case $1 in
            start) start_all ;;
            stop) stop_all ;;
            restart) restart_all ;;
            status) show_status ;;
            start-backend) start_backend ;;
            start-frontend) start_frontend ;;
            stop-backend) stop_backend ;;
            stop-frontend) stop_frontend ;;
            build) build_backend ;;
            *) 
                echo "用法: $0 {start|stop|restart|status|start-backend|start-frontend|stop-backend|stop-frontend|build}"
                echo "或者直接运行 $0 进入交互模式"
                exit 1
                ;;
        esac
    fi
}

# 执行主函数
main "$@"
