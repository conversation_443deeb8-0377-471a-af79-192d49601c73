@echo off
chcp 65001 >nul
title 汇易达保单监控系统 - 环境检查

echo.
echo ========================================
echo   汇易达保单监控系统 - 环境检查
echo ========================================
echo.

set ERROR_COUNT=0

echo 正在检查开发环境...
echo.

REM 检查Java环境
echo [1] 检查 Java 环境
java -version >nul 2>&1
if errorlevel 1 (
    echo ❌ Java 未安装或未配置到PATH
    echo    请安装 Java 8 或更高版本
    echo    下载地址: https://www.oracle.com/java/technologies/downloads/
    set /a ERROR_COUNT+=1
) else (
    echo ✅ Java 环境正常
    java -version 2>&1 | findstr "version"
)
echo.

REM 检查Maven环境
echo [2] 检查 Maven 环境
mvn --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Maven 未安装或未配置到PATH
    echo    请安装 Maven 3.6 或更高版本
    echo    下载地址: https://maven.apache.org/download.cgi
    set /a ERROR_COUNT+=1
) else (
    echo ✅ Maven 环境正常
    mvn --version 2>&1 | findstr "Apache Maven"
)
echo.

REM 检查Node.js环境
echo [3] 检查 Node.js 环境
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js 未安装或未配置到PATH
    echo    请安装 Node.js 14 或更高版本
    echo    下载地址: https://nodejs.org/
    set /a ERROR_COUNT+=1
) else (
    echo ✅ Node.js 环境正常
    node --version
)
echo.

REM 检查npm环境
echo [4] 检查 npm 环境
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm 未安装
    echo    npm 通常随 Node.js 一起安装
    set /a ERROR_COUNT+=1
) else (
    echo ✅ npm 环境正常
    npm --version
)
echo.

REM 检查Git环境
echo [5] 检查 Git 环境
git --version >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Git 未安装或未配置到PATH
    echo    Git 不是必需的，但建议安装用于版本控制
    echo    下载地址: https://git-scm.com/
) else (
    echo ✅ Git 环境正常
    git --version
)
echo.

REM 检查端口占用
echo [6] 检查端口占用情况
echo 检查后端端口 8080:
netstat -ano | findstr :8080 | findstr LISTENING >nul 2>&1
if not errorlevel 1 (
    echo ⚠️  端口 8080 已被占用
    netstat -ano | findstr :8080 | findstr LISTENING
) else (
    echo ✅ 端口 8080 可用
)

echo 检查前端端口 80:
netstat -ano | findstr :80 | findstr LISTENING >nul 2>&1
if not errorlevel 1 (
    echo ⚠️  端口 80 已被占用
    netstat -ano | findstr :80 | findstr LISTENING
) else (
    echo ✅ 端口 80 可用
)
echo.

REM 检查项目文件
echo [7] 检查项目文件
if exist "pom.xml" (
    echo ✅ 找到 Maven 项目文件 (pom.xml)
) else (
    echo ❌ 未找到 Maven 项目文件 (pom.xml)
    set /a ERROR_COUNT+=1
)

if exist "ruoyi-ui\package.json" (
    echo ✅ 找到前端项目文件 (ruoyi-ui\package.json)
) else (
    echo ❌ 未找到前端项目文件 (ruoyi-ui\package.json)
    set /a ERROR_COUNT+=1
)

if exist "ruoyi-admin\src\main\resources\application.yml" (
    echo ✅ 找到后端配置文件
) else (
    echo ❌ 未找到后端配置文件
    set /a ERROR_COUNT+=1
)
echo.

REM 检查数据库连接配置
echo [8] 检查数据库配置
if exist "ruoyi-admin\src\main\resources\application-druid.yml" (
    echo ✅ 找到数据库配置文件
    echo 数据库配置信息:
    findstr "url:" ruoyi-admin\src\main\resources\application-druid.yml
    findstr "username:" ruoyi-admin\src\main\resources\application-druid.yml
) else (
    echo ❌ 未找到数据库配置文件
    set /a ERROR_COUNT+=1
)
echo.

REM 检查Redis配置
echo [9] 检查 Redis 配置
if exist "ruoyi-admin\src\main\resources\application.yml" (
    echo Redis 配置信息:
    findstr "host:" ruoyi-admin\src\main\resources\application.yml | findstr redis -A 1
    findstr "port:" ruoyi-admin\src\main\resources\application.yml | findstr -A 1 -B 1 6379
) else (
    echo ❌ 未找到应用配置文件
)
echo.

REM 总结
echo ========================================
echo   环境检查完成
echo ========================================
if %ERROR_COUNT% equ 0 (
    echo ✅ 所有必需环境检查通过！
    echo 🚀 可以使用 quick-start.bat 快速启动系统
) else (
    echo ❌ 发现 %ERROR_COUNT% 个问题需要解决
    echo 请根据上述提示安装缺失的环境
)
echo.

echo 💡 提示:
echo   - 确保所有环境变量已正确配置
echo   - 如果端口被占用，请停止相关服务或修改配置
echo   - 数据库和Redis需要单独启动
echo.

echo 按任意键退出...
pause >nul
