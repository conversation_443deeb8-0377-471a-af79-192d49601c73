#!/bin/bash

# 汇易达保单监控系统 - 环境检查脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

ERROR_COUNT=0

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 检查端口是否被占用
port_in_use() {
    local port=$1
    if command_exists lsof; then
        lsof -ti:$port >/dev/null 2>&1
    elif command_exists netstat; then
        netstat -tuln | grep ":$port " >/dev/null 2>&1
    else
        return 1
    fi
}

echo
echo "========================================"
echo "  汇易达保单监控系统 - 环境检查"
echo "========================================"
echo

print_message $BLUE "正在检查开发环境..."
echo

# 检查Java环境
echo "[1] 检查 Java 环境"
if command_exists java; then
    print_message $GREEN "✅ Java 环境正常"
    java -version 2>&1 | head -1
else
    print_message $RED "❌ Java 未安装或未配置到PATH"
    print_message $YELLOW "   请安装 Java 8 或更高版本"
    print_message $YELLOW "   Ubuntu/Debian: sudo apt install openjdk-8-jdk"
    print_message $YELLOW "   CentOS/RHEL: sudo yum install java-1.8.0-openjdk-devel"
    ERROR_COUNT=$((ERROR_COUNT + 1))
fi
echo

# 检查Maven环境
echo "[2] 检查 Maven 环境"
if command_exists mvn; then
    print_message $GREEN "✅ Maven 环境正常"
    mvn --version 2>&1 | head -1
else
    print_message $RED "❌ Maven 未安装或未配置到PATH"
    print_message $YELLOW "   请安装 Maven 3.6 或更高版本"
    print_message $YELLOW "   Ubuntu/Debian: sudo apt install maven"
    print_message $YELLOW "   CentOS/RHEL: sudo yum install maven"
    ERROR_COUNT=$((ERROR_COUNT + 1))
fi
echo

# 检查Node.js环境
echo "[3] 检查 Node.js 环境"
if command_exists node; then
    print_message $GREEN "✅ Node.js 环境正常"
    node --version
else
    print_message $RED "❌ Node.js 未安装或未配置到PATH"
    print_message $YELLOW "   请安装 Node.js 14 或更高版本"
    print_message $YELLOW "   官方安装: https://nodejs.org/"
    print_message $YELLOW "   或使用包管理器安装"
    ERROR_COUNT=$((ERROR_COUNT + 1))
fi
echo

# 检查npm环境
echo "[4] 检查 npm 环境"
if command_exists npm; then
    print_message $GREEN "✅ npm 环境正常"
    npm --version
else
    print_message $RED "❌ npm 未安装"
    print_message $YELLOW "   npm 通常随 Node.js 一起安装"
    ERROR_COUNT=$((ERROR_COUNT + 1))
fi
echo

# 检查Git环境
echo "[5] 检查 Git 环境"
if command_exists git; then
    print_message $GREEN "✅ Git 环境正常"
    git --version
else
    print_message $YELLOW "⚠️  Git 未安装或未配置到PATH"
    print_message $YELLOW "   Git 不是必需的，但建议安装用于版本控制"
    print_message $YELLOW "   Ubuntu/Debian: sudo apt install git"
    print_message $YELLOW "   CentOS/RHEL: sudo yum install git"
fi
echo

# 检查端口占用
echo "[6] 检查端口占用情况"
echo "检查后端端口 8080:"
if port_in_use 8080; then
    print_message $YELLOW "⚠️  端口 8080 已被占用"
    if command_exists lsof; then
        lsof -ti:8080 | head -5
    fi
else
    print_message $GREEN "✅ 端口 8080 可用"
fi

echo "检查前端端口 80:"
if port_in_use 80; then
    print_message $YELLOW "⚠️  端口 80 已被占用"
    if command_exists lsof; then
        lsof -ti:80 | head -5
    fi
else
    print_message $GREEN "✅ 端口 80 可用"
fi
echo

# 检查项目文件
echo "[7] 检查项目文件"
if [ -f "pom.xml" ]; then
    print_message $GREEN "✅ 找到 Maven 项目文件 (pom.xml)"
else
    print_message $RED "❌ 未找到 Maven 项目文件 (pom.xml)"
    ERROR_COUNT=$((ERROR_COUNT + 1))
fi

if [ -f "ruoyi-ui/package.json" ]; then
    print_message $GREEN "✅ 找到前端项目文件 (ruoyi-ui/package.json)"
else
    print_message $RED "❌ 未找到前端项目文件 (ruoyi-ui/package.json)"
    ERROR_COUNT=$((ERROR_COUNT + 1))
fi

if [ -f "ruoyi-admin/src/main/resources/application.yml" ]; then
    print_message $GREEN "✅ 找到后端配置文件"
else
    print_message $RED "❌ 未找到后端配置文件"
    ERROR_COUNT=$((ERROR_COUNT + 1))
fi
echo

# 检查数据库连接配置
echo "[8] 检查数据库配置"
if [ -f "ruoyi-admin/src/main/resources/application-druid.yml" ]; then
    print_message $GREEN "✅ 找到数据库配置文件"
    echo "数据库配置信息:"
    grep "url:" ruoyi-admin/src/main/resources/application-druid.yml | head -1
    grep "username:" ruoyi-admin/src/main/resources/application-druid.yml | head -1
else
    print_message $RED "❌ 未找到数据库配置文件"
    ERROR_COUNT=$((ERROR_COUNT + 1))
fi
echo

# 检查Redis配置
echo "[9] 检查 Redis 配置"
if [ -f "ruoyi-admin/src/main/resources/application.yml" ]; then
    echo "Redis 配置信息:"
    grep -A 1 "redis:" ruoyi-admin/src/main/resources/application.yml | head -3
else
    print_message $RED "❌ 未找到应用配置文件"
fi
echo

# 检查系统资源
echo "[10] 检查系统资源"
if command_exists free; then
    echo "内存使用情况:"
    free -h | head -2
fi

if command_exists df; then
    echo "磁盘使用情况:"
    df -h . | head -2
fi
echo

# 总结
echo "========================================"
echo "  环境检查完成"
echo "========================================"
if [ $ERROR_COUNT -eq 0 ]; then
    print_message $GREEN "✅ 所有必需环境检查通过！"
    print_message $GREEN "🚀 可以使用 ./quick-start.sh 快速启动系统"
else
    print_message $RED "❌ 发现 $ERROR_COUNT 个问题需要解决"
    print_message $YELLOW "请根据上述提示安装缺失的环境"
fi
echo

print_message $BLUE "💡 提示:"
print_message $BLUE "  - 确保所有环境变量已正确配置"
print_message $BLUE "  - 如果端口被占用，请停止相关服务或修改配置"
print_message $BLUE "  - 数据库和Redis需要单独启动"
print_message $BLUE "  - 建议使用非root用户运行应用"
echo

# 权限检查
echo "[11] 检查脚本权限"
if [ -x "start-all.sh" ]; then
    print_message $GREEN "✅ start-all.sh 有执行权限"
else
    print_message $YELLOW "⚠️  start-all.sh 没有执行权限"
    print_message $YELLOW "   运行: chmod +x start-all.sh"
fi

if [ -x "quick-start.sh" ]; then
    print_message $GREEN "✅ quick-start.sh 有执行权限"
else
    print_message $YELLOW "⚠️  quick-start.sh 没有执行权限"
    print_message $YELLOW "   运行: chmod +x quick-start.sh"
fi

echo
print_message $GREEN "环境检查完成！"
