-- ========================================
-- 汇易达保单监控系统数据库表结构定义(DDL)
-- 数据库类型：StarRocks 3.3.11
-- 创建时间：2025-08-19
-- 说明：适配StarRocks 3.3.11版本，移除AUTO_INCREMENT，使用应用层生成ID
-- ========================================

-- 创建数据库
-- CREATE DATABASE IF NOT EXISTS huida_monitor_platform;
-- USE huida_monitor_platform;

-- ========================================
-- 业务表
-- ========================================

-- 资产文件明细表
DROP TABLE IF EXISTS asset_file_detail;
CREATE TABLE asset_file_detail (
  id bigint  AUTO_INCREMENT COMMENT '自增序号，主键',
  monitor_date date NULL COMMENT '监控日期',
  asset_file_path varchar(500) NULL COMMENT '资产文件路径',
  asset_file_name varchar(200) NULL COMMENT '资产文件名',
  file_create_date date NULL COMMENT '文件生成日期',
  policy_no varchar(20) NULL COMMENT '保单号',
  account_value decimal(18, 2) NULL DEFAULT "0.00" COMMENT '账户价值（保留2位小数，单位：元）',
  accumulated_income decimal(18, 2) NULL DEFAULT "0.00" COMMENT '累计收益（保留2位小数，单位：元）',
  daily_income decimal(18, 2) NULL DEFAULT "0.00" COMMENT '每日收益（保留2位小数，单位：元）',
  daily_interest decimal(18, 2) NULL DEFAULT "0.00" COMMENT '当日利息（保留2位小数，单位：元）',
  accumulated_interest decimal(18, 2) NULL DEFAULT "0.00" COMMENT '累计利息（保留2位小数，单位：元）',
  bonus_income decimal(18, 2) NULL DEFAULT "0.00" COMMENT '加息收益（保留2位小数，单位：元）',
  create_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间，默认系统时间',
  update_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间，配置最后一次修改的时间'
) ENGINE=OLAP 
DUPLICATE KEY(id)
COMMENT "资产文件明细表"
PARTITION BY RANGE(monitor_date) (
    PARTITION p20250101 VALUES [('2025-01-01'), ('2025-02-01')),
    PARTITION p20250201 VALUES [('2025-02-01'), ('2025-03-01')),
    PARTITION p20250301 VALUES [('2025-03-01'), ('2025-04-01')),
    PARTITION p20250401 VALUES [('2025-04-01'), ('2025-05-01')),
    PARTITION p20250501 VALUES [('2025-05-01'), ('2025-06-01')),
    PARTITION p20250601 VALUES [('2025-06-01'), ('2025-07-01')),
    PARTITION p20250701 VALUES [('2025-07-01'), ('2025-08-01')),
    PARTITION p20250801 VALUES [('2025-08-01'), ('2025-09-01')),
    PARTITION p20250901 VALUES [('2025-09-01'), ('2025-10-01')),
    PARTITION p20251001 VALUES [('2025-10-01'), ('2025-11-01')),
    PARTITION p20251101 VALUES [('2025-11-01'), ('2025-12-01')),
    PARTITION p20251201 VALUES [('2025-12-01'), ('2026-01-01'))
)
DISTRIBUTED BY HASH(policy_no) BUCKETS 16
PROPERTIES (
  "replication_num" = "3",
  "storage_format" = "DEFAULT"
);

-- 配置信息表
DROP TABLE IF EXISTS config_info;
CREATE TABLE config_info (
  id bigint AUTO_INCREMENT COMMENT '自增序号，主键',
  config_type varchar(50) NULL COMMENT '配置类型',
  config_def varchar(10) NULL COMMENT '配置定义',
  config_value varchar(100) NULL COMMENT '配置值',
  config_desc varchar(100) NULL COMMENT '配置说明',
  remark varchar(100) NULL COMMENT '备注，记录配置的特殊说明',
  status int NULL DEFAULT "0" COMMENT '配置状态：0=启用，1=禁用',
  create_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（默认当前时间）',
  update_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间，配置最后一次修改的时间'
) ENGINE=OLAP 
DUPLICATE KEY(id)
COMMENT "配置信息表"
DISTRIBUTED BY HASH(id) BUCKETS 1
PROPERTIES (
  "replication_num" = "3",
  "storage_format" = "DEFAULT"
);

-- FTP信息表
DROP TABLE IF EXISTS ftp_info;
CREATE TABLE ftp_info (
  id bigint AUTO_INCREMENT COMMENT '自增序号，主键',
  file_type int NULL DEFAULT "0" COMMENT '文件落地方式：0=本地存储，1=FTP存储',
  ftp_name varchar(100) NULL COMMENT 'FTP名称',
  description varchar(200) NULL COMMENT '描述',
  ftp_credential varchar(10) NULL DEFAULT "P" COMMENT 'FTP认证类型：P=密码，K=密钥',
  ftp_host varchar(50) NULL COMMENT 'FTP地址',
  ftp_port int NULL DEFAULT "22" COMMENT 'FTP端口',
  ftp_username varchar(50) NULL COMMENT 'FTP用户名',
  ftp_password varchar(50) NULL COMMENT 'FTP密码',
  private_key varchar(200) NULL COMMENT '私钥',
  status int NULL DEFAULT "1" COMMENT '状态：0=停用，1=启用',
  file_path varchar(200) NULL COMMENT '文件路径',
  file_name varchar(100) NULL COMMENT '文件名 多个文件后缀不一样，用*表示',
  has_file_header int NULL DEFAULT "1" COMMENT '是否有文件头：0=无文件头，1=有文件头',
  file_content_format varchar(100) NULL COMMENT '文件内容格式，方便数据提取用，0,1,2,3表示对应取值下标，例如："0,1,2,3"',
  create_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（默认当前时间）',
  update_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间，配置最后一次修改的时间'
) ENGINE=OLAP 
DUPLICATE KEY(id)
COMMENT "FTP信息表"
DISTRIBUTED BY HASH(id) BUCKETS 1
PROPERTIES (
  "replication_num" = "3",
  "storage_format" = "DEFAULT"
);

-- 保单监控表
DROP TABLE IF EXISTS policy_monitor;
CREATE TABLE policy_monitor (
  id bigint AUTO_INCREMENT COMMENT '自增序号，主键',
  monitor_date date NULL COMMENT '监控日期',
  policy_no varchar(20) NULL COMMENT '保单号',
  policy_status varchar(10) NULL COMMENT '保单状态'
) ENGINE=OLAP
DUPLICATE KEY(id)
COMMENT "保单监控表"
DISTRIBUTED BY HASH(id) BUCKETS 1
PROPERTIES (
  "replication_num" = "3",
  "storage_format" = "DEFAULT"
);

-- 保单监控明细表
DROP TABLE IF EXISTS policy_monitor_detail;
CREATE TABLE policy_monitor_detail (
  id bigint AUTO_INCREMENT COMMENT '自增序号，主键',
  monitor_date date NULL COMMENT '监控日期',
  policy_no varchar(20) NULL COMMENT '保单号',
  policy_system varchar(20) NULL COMMENT '保单归属系统',
  sales_channel varchar(50) NULL COMMENT '销售渠道',
  product_code varchar(20) NULL COMMENT '产品编码',
  product_name varchar(100) NULL COMMENT '产品名称',
  policy_status int NULL COMMENT '保单状态：0=有效，1=暂停，2=失效',
  pricing_status int NULL DEFAULT "2" COMMENT '计价状态：0=已计价，1=未计价，2=不涉及',
  new_policy_core_status int NULL DEFAULT "2" COMMENT '新单进核心状态：0=已进核心，1=未进核心，2=不涉及',
  policy_change_core_status int NULL DEFAULT "2" COMMENT '保全进核心状态：0=已进核心，1=未进核心，2=不涉及',
  policy_change_queue_status int NULL DEFAULT "2" COMMENT '保全出队列状态：0=已出队列，1=未出队列，2=不涉及',
  pricing_date date NULL COMMENT '计价日期，最后一次成功计价的日期（未计价或无需计价则为NULL）',
  risk_insurance_status int NULL DEFAULT "2" COMMENT '风险保费状态：0=已扣除，1=未扣除，2=不涉及',
  risk_insurance_date date NULL COMMENT '风险保费扣除日期，最后一次成功扣除的日期（未扣除或无需扣除则为NULL）',
  interest_settlement_status int NULL DEFAULT "2" COMMENT '结息状态：0=已结息，1=未结息，2=不涉及',
  interest_settlement_date date NULL COMMENT '结息日期，最后一次成功结息的日期（未结息或无需结息则为NULL）',
  is_push_website int NULL DEFAULT "0" COMMENT '是否推送官网：0=否，1=是',
  asset_push_website_status int NULL DEFAULT "2" COMMENT '资产推送官网状态：0=已推送，1=未推送，2=不涉及',
  asset_push_website_date date NULL COMMENT '资产推送官网日期，最后一次成功推送资产的日期（未推送或无需推送则为NULL）',
  website_account_value decimal(18, 2) NULL DEFAULT "0.00" COMMENT '资产推送官网账户价值（保留2位小数，单位：元）',
  website_accumulated_income decimal(18, 2) NULL DEFAULT "0.00" COMMENT '资产推送官网累计收益（保留2位小数，单位：元）',
  website_daily_income decimal(18, 2) NULL DEFAULT "0.00" COMMENT '资产推送官网每日收益（保留2位小数，单位：元）',
  website_daily_interest decimal(18, 2) NULL DEFAULT "0.00" COMMENT '资产推送官网当日利息（保留2位小数，单位：元）',
  website_accumulated_interest decimal(18, 2) NULL DEFAULT "0.00" COMMENT '资产推送官网累计利息（保留2位小数，单位：元）',
  website_bonus_income decimal(18, 2) NULL DEFAULT "0.00" COMMENT '资产推送官网加息收益（保留2位小数，单位：元）',
  is_push_microsite int NULL DEFAULT "0" COMMENT '是否推送官微：0=否，1=是',
  asset_push_microsite_status int NULL DEFAULT "2" COMMENT '资产推送官微状态：0=已推送，1=未推送，2=不涉及',
  asset_push_microsite_date date NULL COMMENT '资产推送官微日期，最后一次成功推送资产的日期（未推送或无需推送则为NULL）',
  microsite_account_value decimal(18, 2) NULL DEFAULT "0.00" COMMENT '资产推送官微账户价值（保留2位小数，单位：元）',
  microsite_accumulated_income decimal(18, 2) NULL DEFAULT "0.00" COMMENT '资产推送官微累计收益（保留2位小数，单位：元）',
  microsite_daily_income decimal(18, 2) NULL DEFAULT "0.00" COMMENT '资产推送官微每日收益（保留2位小数，单位：元）',
  microsite_daily_interest decimal(18, 2) NULL DEFAULT "0.00" COMMENT '资产推送官微当日利息（保留2位小数，单位：元）',
  microsite_accumulated_interest decimal(18, 2) NULL DEFAULT "0.00" COMMENT '资产推送官微累计利息（保留2位小数，单位：元）',
  microsite_bonus_income decimal(18, 2) NULL DEFAULT "0.00" COMMENT '资产推送官微加息收益（保留2位小数，单位：元）',
  is_push_trade int NULL DEFAULT "0" COMMENT '是否推送交易：0=否，1=是',
  asset_push_trade_status int NULL DEFAULT "2" COMMENT '资产推送交易状态：0=已推送，1=未推送，2=不涉及',
  asset_push_trade_date date NULL COMMENT '资产推送交易日期，最后一次成功推送资产的日期（未推送或无需推送则为NULL）',
  trade_account_value decimal(18, 2) NULL DEFAULT "0.00" COMMENT '资产推送交易账户价值（保留2位小数，单位：元）',
  trade_accumulated_income decimal(18, 2) NULL DEFAULT "0.00" COMMENT '资产推送交易累计收益（保留2位小数，单位：元）',
  trade_daily_income decimal(18, 2) NULL DEFAULT "0.00" COMMENT '资产推送交易每日收益（保留2位小数，单位：元）',
  trade_daily_interest decimal(18, 2) NULL DEFAULT "0.00" COMMENT '资产推送交易当日利息（保留2位小数，单位：元）',
  trade_accumulated_interest decimal(18, 2) NULL DEFAULT "0.00" COMMENT '资产推送交易累计利息（保留2位小数，单位：元）',
  trade_bonus_income decimal(18, 2) NULL DEFAULT "0.00" COMMENT '资产推送交易加息收益（保留2位小数，单位：元）',
  is_push_channel int NULL DEFAULT "0" COMMENT '是否推送渠道：0=否，1=是',
  asset_push_channel_method int NULL DEFAULT "0" COMMENT '资产推送渠道方式，0=文件，1=接口',
  asset_push_channel_status int NULL DEFAULT "2" COMMENT '资产推送渠道状态：0=已推送，1=未推送，2=不涉及',
  asset_push_channel_date date NULL COMMENT '资产推送渠道日期，最后一次成功推送资产的日期（未推送或无需推送则为NULL）',
  channel_account_value decimal(18, 2) NULL DEFAULT "0.00" COMMENT '资产推送渠道账户价值（保留2位小数，单位：元）',
  channel_accumulated_income decimal(18, 2) NULL DEFAULT "0.00" COMMENT '资产推送渠道累计收益（保留2位小数，单位：元）',
  channel_daily_income decimal(18, 2) NULL DEFAULT "0.00" COMMENT '资产推送渠道每日收益（保留2位小数，单位：元）',
  channel_daily_interest decimal(18, 2) NULL DEFAULT "0.00" COMMENT '资产推送渠道当日利息（保留2位小数，单位：元）',
  channel_accumulated_interest decimal(18, 2) NULL DEFAULT "0.00" COMMENT '资产推送渠道累计利息（保留2位小数，单位：元）',
  channel_bonus_income decimal(18, 2) NULL DEFAULT "0.00" COMMENT '资产推送渠道加息收益（保留2位小数，单位：元）',
  create_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（默认当前时间）',
  update_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间，配置最后一次修改的时间'
) ENGINE=OLAP 
DUPLICATE KEY(id)
COMMENT "保单监控明细表"
PARTITION BY RANGE(monitor_date) (
    PARTITION p20250101 VALUES [('2025-01-01'), ('2025-02-01')),
    PARTITION p20250201 VALUES [('2025-02-01'), ('2025-03-01')),
    PARTITION p20250301 VALUES [('2025-03-01'), ('2025-04-01')),
    PARTITION p20250401 VALUES [('2025-04-01'), ('2025-05-01')),
    PARTITION p20250501 VALUES [('2025-05-01'), ('2025-06-01')),
    PARTITION p20250601 VALUES [('2025-06-01'), ('2025-07-01')),
    PARTITION p20250701 VALUES [('2025-07-01'), ('2025-08-01')),
    PARTITION p20250801 VALUES [('2025-08-01'), ('2025-09-01')),
    PARTITION p20250901 VALUES [('2025-09-01'), ('2025-10-01')),
    PARTITION p20251001 VALUES [('2025-10-01'), ('2025-11-01')),
    PARTITION p20251101 VALUES [('2025-11-01'), ('2025-12-01')),
    PARTITION p20251201 VALUES [('2025-12-01'), ('2026-01-01'))
)
DISTRIBUTED BY HASH(policy_no) BUCKETS 32
PROPERTIES (
  "replication_num" = "3",
  "storage_format" = "DEFAULT"
);

-- 监控汇总结果表
DROP TABLE IF EXISTS monitor_summary_result;
CREATE TABLE monitor_summary_result (
  id bigint AUTO_INCREMENT COMMENT '自增序号，主键',
  monitor_date date NULL COMMENT '监控日期',
  total_policies int NULL DEFAULT "0" COMMENT '总保单数',
  pricing_total int NULL DEFAULT "0" COMMENT '计价总数',
  pricing_success int NULL DEFAULT "0" COMMENT '计价成功数',
  pricing_failed int NULL DEFAULT "0" COMMENT '计价失败数',
  pricing_not_applicable int NULL DEFAULT "0" COMMENT '计价不涉及数',
  risk_insurance_total int NULL DEFAULT "0" COMMENT '风险保费总数',
  risk_insurance_success int NULL DEFAULT "0" COMMENT '风险保费成功数',
  risk_insurance_failed int NULL DEFAULT "0" COMMENT '风险保费失败数',
  risk_insurance_not_applicable int NULL DEFAULT "0" COMMENT '风险保费不涉及数',
  interest_total int NULL DEFAULT "0" COMMENT '结息总数',
  interest_success int NULL DEFAULT "0" COMMENT '结息成功数',
  interest_failed int NULL DEFAULT "0" COMMENT '结息失败数',
  interest_not_applicable int NULL DEFAULT "0" COMMENT '结息不涉及数',
  asset_push_website_total int NULL DEFAULT "0" COMMENT '资产推送官网总数',
  asset_push_website_success int NULL DEFAULT "0" COMMENT '资产推送官网成功数',
  asset_push_website_failed int NULL DEFAULT "0" COMMENT '资产推送官网失败数',
  asset_push_website_not_applicable int NULL DEFAULT "0" COMMENT '资产推送官网不涉及数',
  asset_push_microsite_total int NULL DEFAULT "0" COMMENT '资产推送官微总数',
  asset_push_microsite_success int NULL DEFAULT "0" COMMENT '资产推送官微成功数',
  asset_push_microsite_failed int NULL DEFAULT "0" COMMENT '资产推送官微失败数',
  asset_push_microsite_not_applicable int NULL DEFAULT "0" COMMENT '资产推送官微不涉及数',
  asset_push_trade_total int NULL DEFAULT "0" COMMENT '资产推送交易总数',
  asset_push_trade_success int NULL DEFAULT "0" COMMENT '资产推送交易成功数',
  asset_push_trade_failed int NULL DEFAULT "0" COMMENT '资产推送交易失败数',
  asset_push_trade_not_applicable int NULL DEFAULT "0" COMMENT '资产推送交易不涉及数',
  asset_push_channel_total int NULL DEFAULT "0" COMMENT '资产推送渠道总数',
  asset_push_channel_success int NULL DEFAULT "0" COMMENT '资产推送渠道成功数',
  asset_push_channel_failed int NULL DEFAULT "0" COMMENT '资产推送渠道失败数',
  asset_push_channel_not_applicable int NULL DEFAULT "0" COMMENT '资产推送渠道不涉及数',
  create_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=OLAP
DUPLICATE KEY(id)
COMMENT "监控汇总结果表"
DISTRIBUTED BY HASH(id) BUCKETS 1
PROPERTIES (
  "replication_num" = "3",
  "storage_format" = "DEFAULT"
);

-- ========================================
-- 系统管理表
-- ========================================

-- 批处理任务配置表
DROP TABLE IF EXISTS batch_task_config;
CREATE TABLE batch_task_config (
  id bigint AUTO_INCREMENT COMMENT '自增序号，主键',
  task_code varchar(100) NULL COMMENT '任务编码',
  task_name varchar(100) NULL COMMENT '任务名称',
  task_class varchar(200) NULL COMMENT '任务类名',
  cron_expression varchar(50) NULL COMMENT 'Cron表达式',
  task_params varchar(65533) NULL DEFAULT "{}" COMMENT '任务参数',
  task_status int NULL DEFAULT "1" COMMENT '任务状态：0=停用，1=启用',
  description varchar(500) NULL COMMENT '任务描述',
  create_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（默认当前时间）',
  update_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间，配置最后一次修改的时间'
) ENGINE=OLAP
DUPLICATE KEY(id)
COMMENT "批处理任务配置表"
DISTRIBUTED BY HASH(id) BUCKETS 1
PROPERTIES (
  "replication_num" = "3",
  "storage_format" = "DEFAULT"
);

-- 批处理任务日志表
DROP TABLE IF EXISTS batch_task_log;
CREATE TABLE batch_task_log (
  id bigint  AUTO_INCREMENT COMMENT '自增序号，主键',
  task_code varchar(100) NULL COMMENT '任务编码',
  task_name varchar(100) NULL COMMENT '任务名称',
  start_time datetime NULL COMMENT '开始时间',
  end_time datetime NULL COMMENT '结束时间',
  execution_time bigint NULL DEFAULT "0" COMMENT '执行时长（毫秒）',
  task_status int NULL DEFAULT "2" COMMENT '执行状态：0=失败，1=成功，2=执行中',
  message varchar(65533) NULL COMMENT '执行结果信息和错误信息',
  create_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（默认当前时间）',
  update_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=OLAP
DUPLICATE KEY(id)
COMMENT "批处理任务日志表"
DISTRIBUTED BY HASH(id) BUCKETS 1
PROPERTIES (
  "replication_num" = "3",
  "storage_format" = "DEFAULT"
);

-- 部门表
DROP TABLE IF EXISTS sys_dept;
CREATE TABLE sys_dept (
  dept_id bigint AUTO_INCREMENT COMMENT '部门ID',
  parent_id bigint NULL DEFAULT "0" COMMENT '父部门id',
  ancestors varchar(50) NULL DEFAULT "" COMMENT '祖级列表',
  dept_name varchar(30) NULL DEFAULT "" COMMENT '部门名称',
  order_num int NULL DEFAULT "0" COMMENT '显示顺序',
  leader varchar(20) NULL COMMENT '负责人',
  phone varchar(11) NULL COMMENT '联系电话',
  email varchar(50) NULL COMMENT '邮箱',
  status char(1) NULL DEFAULT "0" COMMENT '部门状态（0正常 1停用）',
  del_flag char(1) NULL DEFAULT "0" COMMENT '删除标志（0代表存在 2代表删除）',
  create_by varchar(64) NULL DEFAULT "" COMMENT '创建者',
  create_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_by varchar(64) NULL DEFAULT "" COMMENT '更新者',
  update_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=OLAP
DUPLICATE KEY(dept_id)
COMMENT "部门表"
DISTRIBUTED BY HASH(dept_id) BUCKETS 1
PROPERTIES (
  "replication_num" = "3",
  "storage_format" = "DEFAULT"
);

-- 角色信息表
DROP TABLE IF EXISTS sys_role;
CREATE TABLE sys_role (
  role_id bigint AUTO_INCREMENT COMMENT '角色ID',
  role_name varchar(30) NOT NULL COMMENT '角色名称',
  role_key varchar(100) NOT NULL COMMENT '角色权限字符串',
  role_sort int COMMENT '显示顺序',
  data_scope char(1) NULL DEFAULT "1" COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  menu_check_strictly tinyint NULL DEFAULT "1" COMMENT '菜单树选择项是否关联显示',
  dept_check_strictly tinyint NULL DEFAULT "1" COMMENT '部门树选择项是否关联显示',
  status char(1) NOT NULL DEFAULT "0" COMMENT '角色状态（0正常 1停用）',
  del_flag char(1) NULL DEFAULT "0" COMMENT '删除标志（0代表存在 2代表删除）',
  create_by varchar(64) NULL DEFAULT "" COMMENT '创建者',
  create_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_by varchar(64) NULL DEFAULT "" COMMENT '更新者',
  update_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  remark varchar(500) NULL COMMENT '备注'
) ENGINE=OLAP
DUPLICATE KEY(role_id)
COMMENT "角色信息表"
DISTRIBUTED BY HASH(role_id) BUCKETS 1
PROPERTIES (
  "replication_num" = "3",
  "storage_format" = "DEFAULT"
);

-- 用户信息表
DROP TABLE IF EXISTS sys_user;
CREATE TABLE sys_user (
  user_id bigint AUTO_INCREMENT COMMENT '用户ID，主键',
  dept_id bigint NULL COMMENT '部门ID',
  user_name varchar(30) NOT NULL COMMENT '用户账号',
  nick_name varchar(30) NOT NULL COMMENT '用户昵称',
  user_type varchar(2) NULL DEFAULT "00" COMMENT '用户类型（00系统用户）',
  email varchar(50) NULL DEFAULT "" COMMENT '用户邮箱',
  phone_number varchar(11) NULL DEFAULT "" COMMENT '手机号码',
  sex char(1) NULL DEFAULT "0" COMMENT '用户性别（0男 1女 2未知）',
  avatar varchar(100) NULL DEFAULT "" COMMENT '头像地址',
  password varchar(100) NULL DEFAULT "" COMMENT '密码',
  status char(1) NULL DEFAULT "0" COMMENT '帐号状态（0正常 1停用）',
  del_flag char(1) NULL DEFAULT "0" COMMENT '删除标志（0代表存在 2代表删除）',
  login_ip varchar(128) NULL DEFAULT "" COMMENT '最后登录IP',
  login_date datetime NULL COMMENT '最后登录时间',
  pwd_update_date datetime NULL COMMENT '密码最后更新时间',
  create_by varchar(64) NULL DEFAULT "" COMMENT '创建者',
  create_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_by varchar(64) NULL DEFAULT "" COMMENT '更新者',
  update_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  remark varchar(500) NULL COMMENT '备注'
) ENGINE=OLAP
DUPLICATE KEY(user_id)
COMMENT "用户信息表"
DISTRIBUTED BY HASH(user_id) BUCKETS 1
PROPERTIES (
  "replication_num" = "3",
  "storage_format" = "DEFAULT"
);

-- 菜单权限表
DROP TABLE IF EXISTS sys_menu;
CREATE TABLE sys_menu (
  menu_id bigint AUTO_INCREMENT COMMENT '菜单ID',
  menu_name varchar(50) NOT NULL COMMENT '菜单名称',
  parent_id bigint NULL DEFAULT "0" COMMENT '父菜单ID',
  order_num int NULL DEFAULT "0" COMMENT '显示顺序',
  path varchar(200) NULL DEFAULT "" COMMENT '路由地址',
  component varchar(255) NULL COMMENT '组件路径',
  query varchar(255) NULL COMMENT '路由参数',
  route_name varchar(50) NULL DEFAULT "" COMMENT '路由名称',
  is_frame int NULL DEFAULT "1" COMMENT '是否为外链（0是 1否）',
  is_cache int NULL DEFAULT "0" COMMENT '是否缓存（0缓存 1不缓存）',
  menu_type char(1) NULL DEFAULT "C" COMMENT '菜单类型（M目录 C菜单 F按钮）',
  visible char(1) NULL DEFAULT "0" COMMENT '菜单状态（0显示 1隐藏）',
  status char(1) NULL DEFAULT "0" COMMENT '菜单状态（0正常 1停用）',
  perms varchar(100) NULL DEFAULT "" COMMENT '权限标识',
  icon varchar(100) NULL DEFAULT "#" COMMENT '菜单图标',
  create_by varchar(64) NULL DEFAULT "" COMMENT '创建者',
  create_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_by varchar(64) NULL DEFAULT "" COMMENT '更新者',
  update_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  remark varchar(500) NULL DEFAULT "" COMMENT '备注'
) ENGINE=OLAP
DUPLICATE KEY(menu_id)
COMMENT "菜单权限表"
DISTRIBUTED BY HASH(menu_id) BUCKETS 1
PROPERTIES (
  "replication_num" = "3",
  "storage_format" = "DEFAULT"
);

-- 关联表设计
-- 用户角色关联表
DROP TABLE IF EXISTS sys_user_role;
CREATE TABLE sys_user_role (
  user_id bigint  COMMENT '用户ID',
  role_id bigint  COMMENT '角色ID'
) ENGINE=OLAP
DUPLICATE KEY(user_id, role_id)
COMMENT "用户角色关联表"
DISTRIBUTED BY HASH(user_id) BUCKETS 1
PROPERTIES (
  "replication_num" = "3",
  "storage_format" = "DEFAULT"
);

-- 角色菜单关联表
DROP TABLE IF EXISTS sys_role_menu;
CREATE TABLE sys_role_menu (
  role_id bigint  COMMENT '角色ID',
  menu_id bigint  COMMENT '菜单ID'
) ENGINE=OLAP
DUPLICATE KEY(role_id, menu_id)
COMMENT "角色菜单关联表"
DISTRIBUTED BY HASH(role_id) BUCKETS 1
PROPERTIES (
  "replication_num" = "3",
  "storage_format" = "DEFAULT"
);

-- 角色部门关联表
DROP TABLE IF EXISTS sys_role_dept;
CREATE TABLE sys_role_dept (
  role_id bigint  COMMENT '角色ID',
  dept_id bigint  COMMENT '部门ID'
) ENGINE=OLAP
DUPLICATE KEY(role_id, dept_id)
COMMENT "角色部门关联表"
DISTRIBUTED BY HASH(role_id) BUCKETS 1
PROPERTIES (
  "replication_num" = "3",
  "storage_format" = "DEFAULT"
);

-- 登录日志表
DROP TABLE IF EXISTS sys_logininfor;
CREATE TABLE sys_logininfor (
  info_id bigint  AUTO_INCREMENT COMMENT '日志ID，自增序号',
  user_name varchar(50) NULL COMMENT '用户账号',
  ipaddr varchar(128) NULL COMMENT '登录IP地址',
  login_location varchar(255) NULL COMMENT '登录地点',
  browser varchar(50) NULL COMMENT '浏览器类型',
  os varchar(50) NULL COMMENT '操作系统',
  status char(1) NULL DEFAULT "0" COMMENT '登录状态（0成功 1失败）',
  msg varchar(255) NULL COMMENT '提示消息',
  login_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间'
) ENGINE=OLAP
DUPLICATE KEY(info_id)
COMMENT "登录日志表"
DISTRIBUTED BY HASH(info_id) BUCKETS 1
PROPERTIES (
  "replication_num" = "3",
  "storage_format" = "DEFAULT"
);

-- 操作日志表
DROP TABLE IF EXISTS sys_oper_log;
CREATE TABLE sys_oper_log (
  oper_id bigint  AUTO_INCREMENT COMMENT '日志ID，自增序号',
  title varchar(50) NULL COMMENT '模块标题',
  business_type int NULL DEFAULT "0" COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  method varchar(100) NULL COMMENT '方法名称',
  request_method varchar(10) NULL COMMENT '请求方式',
  operator_type int NULL DEFAULT "0" COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  oper_name varchar(50) NULL COMMENT '操作人员',
  dept_name varchar(50) NULL COMMENT '部门名称',
  oper_url varchar(255) NULL COMMENT '请求URL',
  oper_ip varchar(128) NULL COMMENT '主机地址',
  oper_location varchar(255) NULL COMMENT '操作地点',
  oper_param varchar(2000) NULL COMMENT '请求参数',
  json_result varchar(2000) NULL COMMENT '返回参数',
  status int NULL DEFAULT "0" COMMENT '操作状态（0正常 1异常）',
  error_msg varchar(2000) NULL COMMENT '错误消息',
  oper_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间'
) ENGINE=OLAP
DUPLICATE KEY(oper_id)
COMMENT "操作日志表"
DISTRIBUTED BY HASH(oper_id) BUCKETS 1
PROPERTIES (
  "replication_num" = "3",
  "storage_format" = "DEFAULT"
);

-- 系统配置表
DROP TABLE IF EXISTS sys_config;
CREATE TABLE sys_config (
  config_id bigint AUTO_INCREMENT COMMENT '参数主键',
  config_name varchar(100) NULL DEFAULT "" COMMENT '参数名称',
  config_key varchar(100) NULL DEFAULT "" COMMENT '参数键名',
  config_value varchar(500) NULL DEFAULT "" COMMENT '参数键值',
  config_type char(1) NULL DEFAULT "N" COMMENT '系统内置（Y是 N否）',
  create_by varchar(64) NULL DEFAULT "" COMMENT '创建者',
  create_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_by varchar(64) NULL DEFAULT "" COMMENT '更新者',
  update_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  remark varchar(500) NULL COMMENT '备注'
) ENGINE=OLAP
DUPLICATE KEY(config_id)
COMMENT "参数配置表"
DISTRIBUTED BY HASH(config_id) BUCKETS 1
PROPERTIES (
  "replication_num" = "3",
  "storage_format" = "DEFAULT"
);

-- 岗位信息表
DROP TABLE IF EXISTS sys_post;
CREATE TABLE sys_post (
  post_id bigint AUTO_INCREMENT COMMENT '岗位ID',
  post_code varchar(64) NOT NULL COMMENT '岗位编码',
  post_name varchar(50) NOT NULL COMMENT '岗位名称',
  post_sort int NOT NULL COMMENT '显示顺序',
  status char(1) NOT NULL DEFAULT "0" COMMENT '状态（0正常 1停用）',
  create_by varchar(64) NULL DEFAULT "" COMMENT '创建者',
  create_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_by varchar(64) NULL DEFAULT "" COMMENT '更新者',
  update_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  remark varchar(500) NULL COMMENT '备注'
) ENGINE=OLAP
DUPLICATE KEY(post_id)
COMMENT "岗位信息表"
DISTRIBUTED BY HASH(post_id) BUCKETS 1
PROPERTIES (
  "replication_num" = "3",
  "storage_format" = "DEFAULT"
);

-- 用户岗位关联表
DROP TABLE IF EXISTS sys_user_post;
CREATE TABLE sys_user_post (
  user_id bigint COMMENT '用户ID',
  post_id bigint COMMENT '岗位ID'
) ENGINE=OLAP
DUPLICATE KEY(user_id, post_id)
COMMENT "用户岗位关联表"
DISTRIBUTED BY HASH(user_id) BUCKETS 1
PROPERTIES (
  "replication_num" = "3",
  "storage_format" = "DEFAULT"
);

-- 用户角色关联表
DROP TABLE IF EXISTS sys_user_role;
CREATE TABLE sys_user_role (
  user_id bigint COMMENT '用户ID',
  role_id bigint COMMENT '角色ID'
) ENGINE=OLAP
DUPLICATE KEY(user_id, role_id)
COMMENT "用户角色关联表"
DISTRIBUTED BY HASH(user_id) BUCKETS 1
PROPERTIES (
  "replication_num" = "3",
  "storage_format" = "DEFAULT"
);

-- 角色菜单关联表
DROP TABLE IF EXISTS sys_role_menu;
CREATE TABLE sys_role_menu (
  role_id bigint COMMENT '角色ID',
  menu_id bigint COMMENT '菜单ID'
) ENGINE=OLAP
DUPLICATE KEY(role_id, menu_id)
COMMENT "角色菜单关联表"
DISTRIBUTED BY HASH(role_id) BUCKETS 1
PROPERTIES (
  "replication_num" = "3",
  "storage_format" = "DEFAULT"
);

-- 角色部门关联表
DROP TABLE IF EXISTS sys_role_dept;
CREATE TABLE sys_role_dept (
  role_id bigint COMMENT '角色ID',
  dept_id bigint COMMENT '部门ID'
) ENGINE=OLAP
DUPLICATE KEY(role_id, dept_id)
COMMENT "角色部门关联表"
DISTRIBUTED BY HASH(role_id) BUCKETS 1
PROPERTIES (
  "replication_num" = "3",
  "storage_format" = "DEFAULT"
);

-- 字典类型表
DROP TABLE IF EXISTS sys_dict_type;
CREATE TABLE sys_dict_type (
  dict_id bigint AUTO_INCREMENT COMMENT '字典主键',
  dict_name varchar(100) NULL DEFAULT "" COMMENT '字典名称',
  dict_type varchar(100) NULL DEFAULT "" COMMENT '字典类型',
  status char(1) NULL DEFAULT "0" COMMENT '状态（0正常 1停用）',
  create_by varchar(64) NULL DEFAULT "" COMMENT '创建者',
  create_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_by varchar(64) NULL DEFAULT "" COMMENT '更新者',
  update_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  remark varchar(500) NULL COMMENT '备注'
) ENGINE=OLAP
DUPLICATE KEY(dict_id)
COMMENT "字典类型表"
DISTRIBUTED BY HASH(dict_id) BUCKETS 1
PROPERTIES (
  "replication_num" = "3",
  "storage_format" = "DEFAULT"
);

-- 字典数据表
DROP TABLE IF EXISTS sys_dict_data;
CREATE TABLE sys_dict_data (
  dict_code bigint AUTO_INCREMENT COMMENT '字典编码',
  dict_sort int NULL DEFAULT "0" COMMENT '字典排序',
  dict_label varchar(100) NULL DEFAULT "" COMMENT '字典标签',
  dict_value varchar(100) NULL DEFAULT "" COMMENT '字典键值',
  dict_type varchar(100) NULL DEFAULT "" COMMENT '字典类型',
  css_class varchar(100) NULL COMMENT '样式属性（其他样式扩展）',
  list_class varchar(100) NULL COMMENT '表格回显样式',
  is_default char(1) NULL DEFAULT "N" COMMENT '是否默认（Y是 N否）',
  status char(1) NULL DEFAULT "0" COMMENT '状态（0正常 1停用）',
  create_by varchar(64) NULL DEFAULT "" COMMENT '创建者',
  create_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_by varchar(64) NULL DEFAULT "" COMMENT '更新者',
  update_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  remark varchar(500) NULL COMMENT '备注'
) ENGINE=OLAP
DUPLICATE KEY(dict_code)
COMMENT "字典数据表"
DISTRIBUTED BY HASH(dict_code) BUCKETS 1
PROPERTIES (
  "replication_num" = "3",
  "storage_format" = "DEFAULT"
);

-- 通知公告表
DROP TABLE IF EXISTS sys_notice;
CREATE TABLE sys_notice (
  notice_id bigint AUTO_INCREMENT COMMENT '公告ID',
  notice_title varchar(50) NOT NULL COMMENT '公告标题',
  notice_type char(1) NOT NULL COMMENT '公告类型（1通知 2公告）',
  notice_content varchar(65533) NULL COMMENT '公告内容',
  status char(1) NULL DEFAULT "0" COMMENT '公告状态（0正常 1关闭）',
  create_by varchar(64) NULL DEFAULT "" COMMENT '创建者',
  create_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_by varchar(64) NULL DEFAULT "" COMMENT '更新者',
  update_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  remark varchar(255) NULL COMMENT '备注'
) ENGINE=OLAP
DUPLICATE KEY(notice_id)
COMMENT "通知公告表"
DISTRIBUTED BY HASH(notice_id) BUCKETS 1
PROPERTIES (
  "replication_num" = "3",
  "storage_format" = "DEFAULT"
);

-- 定时任务表
DROP TABLE IF EXISTS sys_job;
CREATE TABLE sys_job (
  job_id bigint AUTO_INCREMENT COMMENT '任务ID',
  job_name varchar(64) NULL DEFAULT "" COMMENT '任务名称',
  job_group varchar(64) NULL DEFAULT "DEFAULT" COMMENT '任务组名',
  invoke_target varchar(500) NOT NULL COMMENT '调用目标字符串',
  cron_expression varchar(255) NULL DEFAULT "" COMMENT 'cron执行表达式',
  misfire_policy varchar(20) NULL DEFAULT "3" COMMENT '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
  concurrent char(1) NULL DEFAULT "1" COMMENT '是否并发执行（0允许 1禁止）',
  status char(1) NULL DEFAULT "0" COMMENT '状态（0正常 1暂停）',
  create_by varchar(64) NULL DEFAULT "" COMMENT '创建者',
  create_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_by varchar(64) NULL DEFAULT "" COMMENT '更新者',
  update_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  remark varchar(500) NULL DEFAULT "" COMMENT '备注信息'
) ENGINE=OLAP
DUPLICATE KEY(job_id)
COMMENT "定时任务调度表"
DISTRIBUTED BY HASH(job_id) BUCKETS 1
PROPERTIES (
  "replication_num" = "3",
  "storage_format" = "DEFAULT"
);

-- 定时任务日志表
DROP TABLE IF EXISTS sys_job_log;
CREATE TABLE sys_job_log (
  job_log_id bigint AUTO_INCREMENT COMMENT '任务日志ID',
  job_name varchar(64) NOT NULL COMMENT '任务名称',
  job_group varchar(64) NOT NULL COMMENT '任务组名',
  invoke_target varchar(500) NOT NULL COMMENT '调用目标字符串',
  job_message varchar(500) NULL COMMENT '日志信息',
  status char(1) NULL DEFAULT "0" COMMENT '执行状态（0正常 1失败）',
  exception_info varchar(2000) NULL DEFAULT "" COMMENT '异常信息',
  create_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=OLAP
DUPLICATE KEY(job_log_id)
COMMENT "定时任务调度日志表"
DISTRIBUTED BY HASH(job_log_id) BUCKETS 1
PROPERTIES (
  "replication_num" = "3",
  "storage_format" = "DEFAULT"
);

-- 操作日志表
DROP TABLE IF EXISTS sys_oper_log;
CREATE TABLE sys_oper_log (
  oper_id bigint AUTO_INCREMENT COMMENT '日志主键',
  title varchar(50) NULL DEFAULT "" COMMENT '模块标题',
  business_type int NULL DEFAULT "0" COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  method varchar(100) NULL DEFAULT "" COMMENT '方法名称',
  request_method varchar(10) NULL DEFAULT "" COMMENT '请求方式',
  operator_type int NULL DEFAULT "0" COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  oper_name varchar(50) NULL DEFAULT "" COMMENT '操作人员',
  dept_name varchar(50) NULL DEFAULT "" COMMENT '部门名称',
  oper_url varchar(255) NULL DEFAULT "" COMMENT '请求URL',
  oper_ip varchar(128) NULL DEFAULT "" COMMENT '主机地址',
  oper_location varchar(255) NULL DEFAULT "" COMMENT '操作地点',
  oper_param varchar(2000) NULL DEFAULT "" COMMENT '请求参数',
  json_result varchar(2000) NULL DEFAULT "" COMMENT '返回参数',
  status int NULL DEFAULT "0" COMMENT '操作状态（0正常 1异常）',
  error_msg varchar(2000) NULL DEFAULT "" COMMENT '错误消息',
  oper_time datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  cost_time bigint NULL DEFAULT "0" COMMENT '消耗时间'
) ENGINE=OLAP
DUPLICATE KEY(oper_id)
COMMENT "操作日志记录"
DISTRIBUTED BY HASH(oper_id) BUCKETS 1
PROPERTIES (
  "replication_num" = "3",
  "storage_format" = "DEFAULT"
);

-- ========================================
-- 脚本执行完成提示
-- ========================================
SELECT '汇易达保单监控系统数据库表结构创建完成！' AS message;
