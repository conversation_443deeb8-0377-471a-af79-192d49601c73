# 汇易达保单监控系统数据库脚本更新说明

## 版本信息
- **数据库类型**: StarRocks 3.3.11
- **更新时间**: 2025-08-19
- **更新内容**: 基于若依框架重新生成DDL和DML脚本，支持AUTO_INCREMENT

## 更新文件
1. `starrocks_init_ddl_v3311.sql` - 数据库表结构定义
2. `starrocks_init_dml_v3311.sql` - 初始化数据脚本

## 主要变更

### 1. DDL脚本更新 (starrocks_init_ddl_v3311.sql)

#### 新增若依框架系统表
- `sys_post` - 岗位信息表
- `sys_user_post` - 用户岗位关联表
- `sys_user_role` - 用户角色关联表
- `sys_role_menu` - 角色菜单关联表
- `sys_role_dept` - 角色部门关联表
- `sys_dict_type` - 字典类型表
- `sys_dict_data` - 字典数据表
- `sys_notice` - 通知公告表
- `sys_job` - 定时任务表
- `sys_job_log` - 定时任务日志表
- `sys_oper_log` - 操作日志表

#### 更新现有表结构
- **sys_dept**: 添加 `create_by`, `update_by` 字段
- **sys_role**: 添加 `create_by`, `update_by` 字段
- **sys_user**: 添加 `avatar`, `login_ip`, `login_date`, `pwd_update_date`, `create_by`, `update_by`, `remark` 字段
- **sys_menu**: 添加 `route_name`, `create_by`, `update_by` 字段
- **sys_config**: 添加 `create_by`, `update_by` 字段

#### AUTO_INCREMENT支持
所有主键ID字段均使用 `AUTO_INCREMENT` 自动递增，适配StarRocks 3.3.11版本。

### 2. DML脚本更新 (starrocks_init_dml_v3311.sql)

#### 基础数据初始化
- **部门数据**: 汇易达科技组织架构
- **岗位数据**: 董事长、项目经理、人力资源、普通员工
- **角色数据**: 超级管理员、普通角色、监控管理员、监控查看员
- **用户数据**: admin管理员、monitor监控管理员
- **系统配置**: 若依框架标准配置项

#### 字典数据
- 用户性别、菜单状态、系统开关
- 任务状态、任务分组、系统是否
- 通知类型、通知状态、操作类型、系统状态

#### 菜单权限
- **系统管理**: 用户管理、角色管理、菜单管理、部门管理、岗位管理、字典管理、参数设置、通知公告、日志管理
- **系统监控**: 在线用户、定时任务、数据监控、服务监控、缓存监控
- **系统工具**: 表单构建、代码生成、系统接口
- **保单监控**: 保单监控明细、监控汇总结果、资产文件明细、配置信息管理、FTP信息管理、批处理任务配置

#### 权限分配
- **超级管理员**: 拥有所有权限
- **普通角色**: 基础系统功能权限
- **监控管理员**: 保单监控管理权限
- **监控查看员**: 保单监控查看权限

#### 定时任务
- 资产文件解析任务 (每日凌晨1点)
- 保单监控数据更新 (每日凌晨1点30分)
- 监控汇总统计任务 (每日凌晨2点)

## 使用说明

### 1. 执行顺序
```sql
-- 1. 先执行DDL脚本创建表结构
source starrocks_init_ddl_v3311.sql;

-- 2. 再执行DML脚本插入初始数据
source starrocks_init_dml_v3311.sql;
```

### 2. 默认用户
- **管理员账号**: admin / 123456
- **监控账号**: monitor / 123456

### 3. 注意事项
- 业务表结构保持不变，仅更新若依框架相关系统表
- 所有ID字段使用AUTO_INCREMENT，插入数据时无需指定ID值
- 脚本兼容StarRocks 3.3.11版本的AUTO_INCREMENT特性
- 建议在测试环境先验证脚本执行结果

## 兼容性说明
- **StarRocks版本**: 3.3.11及以上
- **若依框架**: 兼容标准若依框架表结构
- **字符集**: UTF-8
- **存储引擎**: OLAP (StarRocks默认)

## 技术特性
- 使用DUPLICATE KEY模型适配StarRocks
- 分布式存储，支持水平扩展
- 副本数设置为3，保证数据可靠性
- 合理的分桶策略，优化查询性能
