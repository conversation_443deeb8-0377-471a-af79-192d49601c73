-- ========================================
-- 汇易达保单监控系统数据库初始化数据(DML)
-- 数据库类型：StarRocks 3.3.11
-- 创建时间：2025-08-19
-- 说明：适配StarRocks 3.3.11版本，支持AUTO_INCREMENT，移除手动指定ID
-- 基于若依框架标准初始化数据
-- ========================================

-- 创建数据库
-- CREATE DATABASE IF NOT EXISTS huida_monitor_platform;
-- USE huida_monitor_platform;

-- ========================================
-- 清理数据（可选）
-- ========================================
-- 清理关联表数据
DELETE FROM sys_user_role WHERE user_id > 0;
DELETE FROM sys_role_menu WHERE role_id > 0;
DELETE FROM sys_role_dept WHERE role_id > 0;
DELETE FROM sys_user_post WHERE user_id > 0;

-- 清理主表数据
DELETE FROM sys_user WHERE user_id > 0;
DELETE FROM sys_role WHERE role_id > 0;
DELETE FROM sys_menu WHERE menu_id > 0;
DELETE FROM sys_dept WHERE dept_id > 0;
DELETE FROM sys_post WHERE post_id > 0;
DELETE FROM sys_config WHERE config_id > 0;
DELETE FROM sys_dict_type WHERE dict_id > 0;
DELETE FROM sys_dict_data WHERE dict_code > 0;
DELETE FROM sys_notice WHERE notice_id > 0;
DELETE FROM sys_job WHERE job_id > 0;

-- ========================================
-- 部门表初始化数据
-- ========================================
INSERT INTO sys_dept (parent_id, ancestors, dept_name, order_num, leader, phone, email, status, del_flag, create_by, create_time, update_by, update_time) VALUES
(0, '0', '汇易达科技', 0, '汇易达', '15888888888', '<EMAIL>', '0', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP),
(100, '0,100', '深圳总公司', 1, '汇易达', '15888888888', '<EMAIL>', '0', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP),
(100, '0,100', '长沙分公司', 2, '汇易达', '15888888888', '<EMAIL>', '0', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP),
(101, '0,100,101', '研发部门', 1, '汇易达', '15888888888', '<EMAIL>', '0', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP),
(101, '0,100,101', '市场部门', 2, '汇易达', '15888888888', '<EMAIL>', '0', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP),
(101, '0,100,101', '测试部门', 3, '汇易达', '15888888888', '<EMAIL>', '0', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP),
(101, '0,100,101', '财务部门', 4, '汇易达', '15888888888', '<EMAIL>', '0', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP),
(101, '0,100,101', '运维部门', 5, '汇易达', '15888888888', '<EMAIL>', '0', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP),
(102, '0,100,102', '市场部门', 1, '汇易达', '15888888888', '<EMAIL>', '0', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP),
(102, '0,100,102', '财务部门', 2, '汇易达', '15888888888', '<EMAIL>', '0', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP);

-- ========================================
-- 岗位信息表初始化数据
-- ========================================
INSERT INTO sys_post (post_code, post_name, post_sort, status, create_by, create_time, update_by, update_time, remark) VALUES
('ceo', '董事长', 1, '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, ''),
('se', '项目经理', 2, '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, ''),
('hr', '人力资源', 3, '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, ''),
('user', '普通员工', 4, '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '');

-- ========================================
-- 角色信息表初始化数据
-- ========================================
INSERT INTO sys_role (role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES
('超级管理员', 'admin', 1, '1', 1, 1, '0', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '超级管理员'),
('普通角色', 'common', 2, '2', 1, 1, '0', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '普通角色'),
('监控管理员', 'monitor_admin', 3, '2', 1, 1, '0', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '保单监控管理员'),
('监控查看员', 'monitor_viewer', 4, '3', 1, 1, '0', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '保单监控查看员');

-- ========================================
-- 用户信息表初始化数据
-- ========================================
INSERT INTO sys_user (dept_id, user_name, nick_name, user_type, email, phone_number, sex, avatar, password, status, del_flag, login_ip, login_date, pwd_update_date, create_by, create_time, update_by, update_time, remark) VALUES
(103, 'admin', '汇易达管理员', '00', '<EMAIL>', '15888888888', '1', '', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '127.0.0.1', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '管理员'),
(105, 'monitor', '监控管理员', '00', '<EMAIL>', '15888888889', '0', '', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '127.0.0.1', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '监控管理员');

-- ========================================
-- 用户角色关联表初始化数据
-- ========================================
INSERT INTO sys_user_role (user_id, role_id) VALUES 
(1, 1),
(2, 3);

-- ========================================
-- 用户岗位关联表初始化数据
-- ========================================
INSERT INTO sys_user_post (user_id, post_id) VALUES 
(1, 1),
(2, 2);

-- ========================================
-- 角色部门关联表初始化数据
-- ========================================
INSERT INTO sys_role_dept (role_id, dept_id) VALUES 
(2, 100),
(2, 101),
(2, 105);

-- ========================================
-- 系统配置表初始化数据
-- ========================================
INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark) VALUES
('主框架页-默认皮肤样式名称', 'sys.index.skinName', 'skin-blue', 'Y', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow'),
('用户管理-账号初始密码', 'sys.user.initPassword', '123456', 'Y', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '初始化密码 123456'),
('主框架页-侧边栏主题', 'sys.index.sideTheme', 'theme-dark', 'Y', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '深色主题theme-dark，浅色主题theme-light'),
('账号自助-验证码开关', 'sys.account.captchaEnabled', 'true', 'Y', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '是否开启验证码功能（true开启，false关闭）'),
('账号自助-是否开启用户注册功能', 'sys.account.registerUser', 'false', 'Y', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '是否开启注册用户功能（true开启，false关闭）'),
('用户登录-黑名单列表', 'sys.login.blackIPList', '', 'Y', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '设置登录IP黑名单限制，多个匹配项以;分隔，支持匹配（*通配、网段）'),
('用户管理-初始密码修改策略', 'sys.account.initPasswordModify', '1', 'Y', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '0：初始密码修改策略关闭，没有任何提示，1：提醒用户，如果未修改初始密码，则在登录时就会提醒修改密码对话框'),
('用户管理-账号密码更新周期', 'sys.account.passwordValidateDays', '0', 'Y', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '密码更新周期（填写数字，数据初始化值为0不限制，若修改必须为大于0小于365的正整数），如果超过这个周期登录系统时，则在登录时就会提醒修改密码对话框');

-- ========================================
-- 字典类型表初始化数据
-- ========================================
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) VALUES
('用户性别', 'sys_user_sex', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '用户性别列表'),
('菜单状态', 'sys_show_hide', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '菜单状态列表'),
('系统开关', 'sys_normal_disable', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '系统开关列表'),
('任务状态', 'sys_job_status', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '任务状态列表'),
('任务分组', 'sys_job_group', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '任务分组列表'),
('系统是否', 'sys_yes_no', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '系统是否列表'),
('通知类型', 'sys_notice_type', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '通知类型列表'),
('通知状态', 'sys_notice_status', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '通知状态列表'),
('操作类型', 'sys_oper_type', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '操作类型列表'),
('系统状态', 'sys_common_status', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '登录状态列表');

-- ========================================
-- 字典数据表初始化数据
-- ========================================
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES
(1, '男', '0', 'sys_user_sex', '', '', 'Y', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '性别男'),
(2, '女', '1', 'sys_user_sex', '', '', 'N', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '性别女'),
(3, '未知', '2', 'sys_user_sex', '', '', 'N', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '性别未知'),
(1, '显示', '0', 'sys_show_hide', '', 'primary', 'Y', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '显示菜单'),
(2, '隐藏', '1', 'sys_show_hide', '', 'danger', 'N', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '隐藏菜单'),
(1, '正常', '0', 'sys_normal_disable', '', 'primary', 'Y', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '正常状态'),
(2, '停用', '1', 'sys_normal_disable', '', 'danger', 'N', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '停用状态'),
(1, '正常', '0', 'sys_job_status', '', 'primary', 'Y', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '正常状态'),
(2, '暂停', '1', 'sys_job_status', '', 'danger', 'N', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '停用状态'),
(1, '默认', 'DEFAULT', 'sys_job_group', '', '', 'Y', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '默认分组'),
(2, '系统', 'SYSTEM', 'sys_job_group', '', '', 'N', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '系统分组'),
(1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '系统默认是'),
(2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '系统默认否'),
(1, '通知', '1', 'sys_notice_type', '', 'warning', 'Y', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '通知'),
(2, '公告', '2', 'sys_notice_type', '', 'success', 'N', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '公告'),
(1, '正常', '0', 'sys_notice_status', '', 'primary', 'Y', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '正常状态'),
(2, '关闭', '1', 'sys_notice_status', '', 'danger', 'N', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '关闭状态'),
(99, '其他', '0', 'sys_oper_type', '', 'info', 'N', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '其他操作'),
(1, '新增', '1', 'sys_oper_type', '', 'info', 'N', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '新增操作'),
(2, '修改', '2', 'sys_oper_type', '', 'info', 'N', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '修改操作'),
(3, '删除', '3', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '删除操作'),
(4, '授权', '4', 'sys_oper_type', '', 'primary', 'N', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '授权操作'),
(5, '导出', '5', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '导出操作'),
(6, '导入', '6', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '导入操作'),
(7, '强退', '7', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '强退操作'),
(8, '生成代码', '8', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '生成操作'),
(9, '清空数据', '9', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '清空操作'),
(1, '成功', '0', 'sys_common_status', '', 'primary', 'N', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '正常状态'),
(2, '失败', '1', 'sys_common_status', '', 'danger', 'N', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '停用状态');

-- ========================================
-- 菜单权限表初始化数据
-- ========================================
-- 一级菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('系统管理', 0, 1, 'system', '', '', '', 1, 0, 'M', '0', '0', '', 'system', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '系统管理目录'),
('系统监控', 0, 2, 'monitor', '', '', '', 1, 0, 'M', '0', '0', '', 'monitor', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '系统监控目录'),
('系统工具', 0, 3, 'tool', '', '', '', 1, 0, 'M', '0', '0', '', 'tool', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '系统工具目录'),
('保单监控', 0, 4, 'policy', '', '', '', 1, 0, 'M', '0', '0', '', 'guide', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '保单监控目录');

-- 系统管理二级菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('用户管理', 1, 1, 'user', 'system/user/index', '', '', 1, 0, 'C', '0', '0', 'system:user:list', 'user', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '用户管理菜单'),
('角色管理', 1, 2, 'role', 'system/role/index', '', '', 1, 0, 'C', '0', '0', 'system:role:list', 'peoples', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '角色管理菜单'),
('菜单管理', 1, 3, 'menu', 'system/menu/index', '', '', 1, 0, 'C', '0', '0', 'system:menu:list', 'tree-table', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '菜单管理菜单'),
('部门管理', 1, 4, 'dept', 'system/dept/index', '', '', 1, 0, 'C', '0', '0', 'system:dept:list', 'tree', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '部门管理菜单'),
('岗位管理', 1, 5, 'post', 'system/post/index', '', '', 1, 0, 'C', '0', '0', 'system:post:list', 'post', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '岗位管理菜单'),
('字典管理', 1, 6, 'dict', 'system/dict/index', '', '', 1, 0, 'C', '0', '0', 'system:dict:list', 'dict', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '字典管理菜单'),
('参数设置', 1, 7, 'config', 'system/config/index', '', '', 1, 0, 'C', '0', '0', 'system:config:list', 'edit', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '参数设置菜单'),
('通知公告', 1, 8, 'notice', 'system/notice/index', '', '', 1, 0, 'C', '0', '0', 'system:notice:list', 'message', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '通知公告菜单'),
('日志管理', 1, 9, 'log', '', '', '', 1, 0, 'M', '0', '0', '', 'log', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '日志管理菜单');

-- 系统监控二级菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('在线用户', 2, 1, 'online', 'monitor/online/index', '', '', 1, 0, 'C', '0', '0', 'monitor:online:list', 'online', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '在线用户菜单'),
('定时任务', 2, 2, 'job', 'monitor/job/index', '', '', 1, 0, 'C', '0', '0', 'monitor:job:list', 'job', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '定时任务菜单'),
('数据监控', 2, 3, 'druid', 'monitor/druid/index', '', '', 1, 0, 'C', '0', '0', 'monitor:druid:list', 'druid', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '数据监控菜单'),
('服务监控', 2, 4, 'server', 'monitor/server/index', '', '', 1, 0, 'C', '0', '0', 'monitor:server:list', 'server', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '服务监控菜单'),
('缓存监控', 2, 5, 'cache', 'monitor/cache/index', '', '', 1, 0, 'C', '0', '0', 'monitor:cache:list', 'redis', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '缓存监控菜单'),
('缓存列表', 2, 6, 'cacheList', 'monitor/cache/list', '', '', 1, 0, 'C', '0', '0', 'monitor:cache:list', 'redis-list', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '缓存列表菜单');

-- 系统工具二级菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('表单构建', 3, 1, 'build', 'tool/build/index', '', '', 1, 0, 'C', '0', '0', 'tool:build:list', 'build', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '表单构建菜单'),
('代码生成', 3, 2, 'gen', 'tool/gen/index', '', '', 1, 0, 'C', '0', '0', 'tool:gen:list', 'code', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '代码生成菜单'),
('系统接口', 3, 3, 'swagger', 'tool/swagger/index', '', '', 1, 0, 'C', '0', '0', 'tool:swagger:list', 'swagger', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '系统接口菜单');

-- 保单监控二级菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('保单监控明细', 4, 1, 'detail', 'policy/detail/index', '', '', 1, 0, 'C', '0', '0', 'policy:detail:list', 'list', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '保单监控明细菜单'),
('监控汇总结果', 4, 2, 'summary', 'policy/summary/index', '', '', 1, 0, 'C', '0', '0', 'policy:summary:list', 'chart', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '监控汇总结果菜单'),
('资产文件明细', 4, 3, 'asset', 'policy/asset/index', '', '', 1, 0, 'C', '0', '0', 'policy:asset:list', 'money', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '资产文件明细菜单'),
('配置信息管理', 4, 4, 'config', 'policy/config/index', '', '', 1, 0, 'C', '0', '0', 'policy:config:list', 'tool', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '配置信息管理菜单'),
('FTP信息管理', 4, 5, 'ftp', 'policy/ftp/index', '', '', 1, 0, 'C', '0', '0', 'policy:ftp:list', 'server', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, 'FTP信息管理菜单'),
('批处理任务配置', 4, 6, 'task', 'policy/task/index', '', '', 1, 0, 'C', '0', '0', 'policy:task:list', 'job', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '批处理任务配置菜单');

-- 三级菜单（日志管理）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('操作日志', 108, 1, 'operlog', 'monitor/operlog/index', '', '', 1, 0, 'C', '0', '0', 'monitor:operlog:list', 'form', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '操作日志菜单'),
('登录日志', 108, 2, 'logininfor', 'monitor/logininfor/index', '', '', 1, 0, 'C', '0', '0', 'monitor:logininfor:list', 'logininfor', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '登录日志菜单');

-- ========================================
-- 角色菜单关联表初始化数据
-- ========================================
-- 超级管理员拥有所有权限（role_id=1自动拥有所有菜单权限）
-- 普通角色权限配置
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
(2, 1), (2, 2), (2, 3), (2, 4),
(2, 100), (2, 101), (2, 102), (2, 103), (2, 104), (2, 105), (2, 106), (2, 107), (2, 108),
(2, 109), (2, 110), (2, 111), (2, 112), (2, 113), (2, 114),
(2, 115), (2, 116), (2, 117),
(2, 400), (2, 401), (2, 402), (2, 403), (2, 404), (2, 405),
(2, 500), (2, 501);

-- 监控管理员权限（保单监控相关功能）
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
(3, 4), (3, 400), (3, 401), (3, 402), (3, 403), (3, 404), (3, 405);

-- 监控查看员权限（只读权限）
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
(4, 4), (4, 400), (4, 401), (4, 402);

-- ========================================
-- 通知公告表初始化数据
-- ========================================
INSERT INTO sys_notice (notice_title, notice_type, notice_content, status, create_by, create_time, update_by, update_time, remark) VALUES
('温馨提醒：汇易达保单监控系统上线啦', '2', '汇易达保单监控系统正式上线，欢迎使用！', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '管理员'),
('维护通知：系统将于每日凌晨进行数据同步', '1', '系统将于每日凌晨1-3点进行数据同步和维护，期间可能影响部分功能使用。', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '管理员');

-- ========================================
-- 定时任务表初始化数据
-- ========================================
INSERT INTO sys_job (job_name, job_group, invoke_target, cron_expression, misfire_policy, concurrent, status, create_by, create_time, update_by, update_time, remark) VALUES
('资产文件解析任务', 'SYSTEM', 'assetFileTask.parseAssetFiles', '0 0 1 * * ?', '3', '1', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '每日凌晨1点执行资产文件解析'),
('保单监控数据更新', 'SYSTEM', 'policyMonitorTask.updateMonitorData', '0 30 1 * * ?', '3', '1', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '每日凌晨1点30分更新保单监控数据'),
('监控汇总统计任务', 'SYSTEM', 'monitorSummaryTask.generateSummary', '0 0 2 * * ?', '3', '1', '0', 'admin', CURRENT_TIMESTAMP, '', CURRENT_TIMESTAMP, '每日凌晨2点生成监控汇总结果');

-- ========================================
-- 脚本执行完成提示
-- ========================================
SELECT '汇易达保单监控系统初始化数据插入完成！' AS message;
