@echo off
chcp 65001 >nul
title 汇易达保单监控系统 - 快速启动

echo.
echo ========================================
echo   汇易达保单监控系统 - 快速启动
echo ========================================
echo.

set BACKEND_JAR=ruoyi-admin.jar
set FRONTEND_DIR=ruoyi-ui

echo 正在检查环境...

REM 检查Java环境
java -version >nul 2>&1
if errorlevel 1 (
    echo ❌ Java 未安装或未配置到PATH
    echo 请安装 Java 8 或更高版本
    pause
    exit /b 1
)
echo ✅ Java 环境正常

REM 检查Node.js环境
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js 未安装或未配置到PATH
    echo 请安装 Node.js 14 或更高版本
    pause
    exit /b 1
)
echo ✅ Node.js 环境正常

REM 检查Maven环境
mvn --version >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Maven 未安装，将跳过构建步骤
    set MAVEN_AVAILABLE=false
) else (
    echo ✅ Maven 环境正常
    set MAVEN_AVAILABLE=true
)

echo.
echo 正在启动服务...

REM 检查并构建后端
if not exist %BACKEND_JAR% (
    if "%MAVEN_AVAILABLE%"=="true" (
        echo 🔨 后端JAR文件不存在，正在构建...
        call mvn clean package -Dmaven.test.skip=true -q
        if errorlevel 1 (
            echo ❌ 后端构建失败
            pause
            exit /b 1
        )
        if exist ruoyi-admin\target\ruoyi-admin.jar (
            copy ruoyi-admin\target\ruoyi-admin.jar . >nul
            echo ✅ 后端构建完成
        ) else (
            echo ❌ 构建失败，未找到JAR文件
            pause
            exit /b 1
        )
    ) else (
        echo ❌ 后端JAR文件不存在且Maven不可用
        echo 请手动构建项目或安装Maven
        pause
        exit /b 1
    )
)

REM 启动后端服务
echo 🚀 启动后端服务...
for /f "tokens=1" %%i in ('jps -l ^| findstr %BACKEND_JAR% 2^>nul') do (
    echo ⚠️  后端服务已在运行 (PID: %%i)
    goto start_frontend
)

start "汇易达后端服务" javaw -Dname=%BACKEND_JAR% -Duser.timezone=Asia/Shanghai -Xms512m -Xmx1024m -jar %BACKEND_JAR%
echo ✅ 后端服务启动中...

:start_frontend
REM 启动前端服务
echo 🚀 启动前端服务...
if not exist %FRONTEND_DIR% (
    echo ❌ 前端目录不存在: %FRONTEND_DIR%
    pause
    exit /b 1
)

cd %FRONTEND_DIR%

REM 检查依赖
if not exist node_modules (
    echo 📦 安装前端依赖...
    call npm install
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        cd ..
        exit /b 1
    )
    echo ✅ 依赖安装完成
)

REM 检查前端是否已运行
netstat -ano | findstr :80 | findstr LISTENING >nul 2>&1
if not errorlevel 1 (
    echo ⚠️  前端服务可能已在运行 (端口80被占用)
) else (
    echo 🚀 启动前端开发服务器...
    start "汇易达前端服务" cmd /k "npm run dev"
)

cd ..

echo.
echo ========================================
echo   启动完成！
echo ========================================
echo.
echo 🌐 前端访问地址: http://localhost
echo 🔧 后端访问地址: http://localhost:8080
echo 📚 API文档地址:  http://localhost:8080/swagger-ui.html
echo 📊 数据库监控:   http://localhost:8080/druid/
echo.
echo 默认登录账号:
echo   用户名: admin
echo   密码:   123456
echo.
echo 💡 提示: 
echo   - 首次启动可能需要等待1-2分钟
echo   - 如遇到问题，请检查端口是否被占用
echo   - 可使用 start-all.bat 进行更多操作
echo.

timeout /t 3 /nobreak >nul
start http://localhost

echo 按任意键退出...
pause >nul
