核心部分梳理
概述
梳理核心部分逻辑，用于监控项目
1.核心保单状态
涉及主要表  lcpol , lccont  , lccontstate
--状态枚举值

select * from ldcode a where a.codetype = 'contavailablereason';
select * from ldcode a where a.codetype = 'contterminatereason';

select * from ldcode a where a.codetype = 'contavailablestate';
select * from ldcode a where a.codetype = 'contterminatestate';

-- 保单状态查询sql

SELECT  a."contno" 保单号, (SELECT  codename from  ldcode where codetype ='selltype'  and  code=a.selltype) 渠道 ,
CASE
WHEN a.appflag = '0' THEN '投保'
WHEN a.appflag = '1' THEN
DECODE((SELECT  DISTINCT 1  -- 确保只返回一个值
FROM lccontstate
WHERE statetype = 'Available'
AND contno = a.contno
AND polno = b.mainpolno
AND state = '1'
AND enddate IS NULL),
'1', '失效',
'承保')
ELSE
DECODE((SELECT MAX(statereason)  -- 使用MAX确保只返回一个值
FROM lccontstate
WHERE statetype = 'Terminate'
AND contno = a.contno
AND polno = b.mainpolno
AND state = '1'
AND enddate IS NULL),
'01', '满期终止',
'02', '退保终止',
'03', '解约终止',
'04', '理赔终止',
'05', '协退终止',
'06', '犹退终止',
'07', '失效终止',
'08', '其他终止')
END AS 保单状态
FROM lccont a
JOIN lcpol b ON a."contno" = b."contno" AND b."polno" = b."mainpolno"
WHERE a."appflag" IN ('1', '4')
AND SUBSTR(b.riskcode, 3, 1) = '3' and  a."contno" ='86000020211602304341'
;


2.1 核心t+2计价
涉及主要表： lcinsureacctrace
-- 抽数sql
select a.polno
from lcpol a,lccont lc
where  a.contno = lc.contno
and exists (select 1
from lcinsureacctrace c
where c.state = '0'    --  未计价状态
and a.polno = c.polno
and c.shouldvaluedate =  to_date('2025-07-11','yyyy-MM-dd')   -- 应计价日期
and exists (select 1
from LOAccUnitPrice d
where d.startdate = c.paydate
and c.insuaccno = d.insuaccno
and d.state = '0'))   --  已公布对应的净值
and not exists
(select 1
from lcinsureacctrace c
where c.state = '0'
and a.polno = c.polno
and (c.unitcount <> 0 or c.money <> 0)   -- 账户金额或份额不等于0
and c.shouldvaluedate = to_date('2025-07-11','yyyy-MM-dd')
and not exists (select 1
from LOAccUnitPrice d
where d.startdate = c.paydate
and d.state = '0'
and c.insuaccno = d.insuaccno))
and not exists (select 1
from lcpol e, lpedoritem lp
where e.polno = a.polno
and e.polno = mainpolno
and e.contno = lp.contno
and e.uintlinkvaliflag = '4'
and lp.edortype = 'WT')  -- 去除犹豫期内不进账户，发生犹退的
and (lc.isPlanFLag <> '1' or lc.isPlanFLag is null)
and a.riskcode = '413211';


2.2 核心贷款结息
涉及主要表  ：lcloanaccclass
select a."contno" ,b."selltype" ,  COUNT(1)
from lcloanaccclass a  join  lccont  b  on  a."contno" =b."contno"     where   -- a.modifydate=TRUNC(sysdate-1)   and    -- 如果只统计增量，看昨天已结息的，今日未结的
a."baladate" <>TRUNC(sysdate-1) -- 所有未结息的
and a.payoffflag=0
GROUP BY a."contno",b."selltype"  ;


交易中心保单，核心生成利息文件路径：

/edoc/Web/TLDailyFile/2025/04/17/TL/04/195

说明：  2025/04/17  是结息时间(跑批处理的时间) ，195是selltype。


2.3 核心推送资产（给交易中心）


核心批处理  100508  ，小金罐等交易中心资产（非支付宝）
核心配置路径
select codename from  ldcode1 where CODETYPE='NewPolicyAmnt;

文件路径  /edoc/Web/NewPolicyAmnt
可以查询生成文件数
find ./*/2024/08/22/ -name *.xml ! -path TaskEndNewCenFile | wc -l
核心落库数据：SELECT   * from  "tbl_insuacc_codenew"   ;


t+1支付宝资产文件：
收益文件          /edoc/Web/NewAccSync/146/2025/07/21/TL.zip
SELECT * from    t_bq_timedtask WHERE starttime>=trunc(sysdate-1) AND tasktype='15' ; -- 19点45执行

明细文件(保全)    /edoc/Web/NewPolicyDetail/146/2025/07/21/TLDetail.zip  
SELECT * from    t_bq_timedtask WHERE starttime>=trunc(sysdate-1) AND tasktype='28' ; -- 20点20执行



3.1 风险保费
涉及的主要表   lcriskacc   lccont   lcpol  lccontstate  lpedoritem

-- 风险保费抽数逻辑
select a.contno,a.polno,a.RiskCode,a.CValiDate,a.amnt,a.InsuredBirthday,a.InsuredSex,a.InsuredNo,a.ManageCom,a.insuredappage
from lcpol a,lccont b
where a.contno=b.contno and a.appflag = '1' -- 未终止
and  riskcode in (select riskcode  from t_cal_business where businesstype='FXBF') -- 需扣减风险保费的险种
and (b.isPlanFLag <> '1' or b.isPlanFLag is null)
and b.selltype not in ('0')
and not exists (select 'X' from lccontstate d where a.contno=d.contno and d.polno=a.mainpolno and d.startdate=
(select max(c.startdate) from lccontstate c where c.contno=a.contno) and d.enddate is null and d.state = '1' and d.statetype = 'Available')  -- 未失效
and not exists (select 'X' from lcinsureacctrace d where d.contno=a.contno and d.busytype='BQ' and d.edortype='RP' and d.state='0')  -- 不存在募集计划变更未计价
and not exists (select 'X' from lcconthangupstate h, lpedoritem p where h.contno = a.contno and p.contno = a.contno and h.contno=p.contno
and h.hanguptype = '2' and h.hangupno = p.edoracceptno and p.edortype in ('WT', 'CT')and p.edorappdate <=to_date('2025-07-07','yyyy-MM-dd') ) --  t-2日之前未退保
and not exists
(
select * from lpedorapptemp where contno=a.contno and
edortype in ('WT', 'CT','EIWT','EICT')
and state='0' and edorappdate <=to_date('2025-07-07','yyyy-MM-dd')
union all
select * from lpedorapptemp where contno=a.contno and
edortype is null
and state='0' and edorappdate <=to_date('2025-07-07','yyyy-MM-dd')
)  -- 不存在队列中的退保
and a.cvalidate <=to_date('2025-07-07','yyyy-MM-dd')  -- 限制生效日期 ， t-2 日已生效
and (((select count(1) from lmriskapp lm,lcpol t where lm.riskcode = t.riskcode and lm.risktype3='3' and t.contno = a.contno )>1
and exists (select 1 from lcinsureacc l where l.contno = a.contno and l.polno = a.polno and l.insuaccbala>0))
or (select count(1) from lmriskapp lm,lcpol t where lm.riskcode = t.riskcode and lm.risktype3='3' and t.contno = a.contno )<=1) -- 投连账户金额大于0
and a.riskcode = '413103'  -- 批处理按险种依次跑
union
select b.contno,b.polno,b.RiskCode,b.CValiDate,b.amnt,b.InsuredBirthday,b.InsuredSex,b.InsuredNo,b.ManageCom,b.insuredappage
from lcriskacc a,lcpol b,lccont c
where a.polno=b.polno and b.contno=c.contno
and a.ShouldBalaDate <= to_date('2025-07-09','yyyy-MM-dd')         --  trace表拆分，抽档预扣除风险保费的保单，进行计价操作
and a.state='0' and  b.riskcode in (select riskcode  from t_cal_business where businesstype='FXBF')
and (c.isPlanFLag <> '1' or c.isPlanFLag is null)
and b.riskcode = '413103';


风险保费结算异常数据：
--  当天风险保费批处理失败数据

SELECT a."dealip"  as 保单号,a.errorinfo as 报错原因, a.* from t_bq_timedtask_error_log   a  where    a."taskid"  in (select  taskid  from t_bq_timedtask  where  "tasktype" ='13' and "starttime" =trunc(sysdate) );


4.1 京东、支付宝保单资产是否推送官网、官微、交易、老网销以及推送逻辑梳理
4.1.1 官微/官网文件批处理
-- 外网账户价值文件同步批处理
SELECT * from ldtask WHERE taskclass='InsureAccToWebTask' ;

文件生成路径：/edoc/PolicyAmnt/143/YY/mm/dd

SELECT policyNo,earning,makeDate FROM asset_earn WHERE policyNo = '86000020191210025095' ORDER BY makeDate desc;

      4.1.2  交易中心京东渠道资产
交易中心京东渠道资产同步批处理：
SELECT * FROM "ldtaskrunlog" WHERE "taskcode" = '100508' and "executedate" >= '2025-07-11' ORDER BY "executedate" desc;

文件生成路径：/edoc/Web/NewPolicyAmnt/

    4.1.3 交易中心支付宝渠道资产
交易中心支付宝渠道资产同步批处理：
SELECT * from t_bq_timedtask WHERE tasktype='15' ORDER BY starttime desc;

文件生成路径：/edoc/Web/NewAccSync/146/

    4.1.4 核心同步老网销
老网销资产提前推送批处理一，二  InsureAccToWebFucTask 按服务器IP区分
结算路径
/edoc/PolicyAmnt/按照渠道生成
/edoc/InsuAcc/按照渠道生成
接数文件路径：
/edoc/PolicyAmnt/TaskEndFucFile
/edoc/InsuAcc/TaskEndFucFile
--抽数sql
select count(1) from lcpol b where  
//--险种结算日期小于当前日期
exists (select 'X' from lcinsureacc a where b.polno = a.polno  and a.baladate<=date'2025-07-22')
//--保单险种有效，且是有效险种
and b.appflag = '1'  and b.riskcode in ('413205','413101')
//--保单没有终止
and not exists (select 'X' from lccontstate g where g.statetype in ('Terminate') and g.state = '1'  and b.contno =g.contno and  g.polno = b.polno and enddate is null)
//--保单有效
and exists (select 'X' from lccontstate h where h.statetype in ('Available') and h.state = '0'  and b.contno =h.contno and h.polno = b.polno and enddate is null)
//-- 网销线上保单
and exists(select 1 from lccont t where  t.SaleChnl='04' and t.selltype='20' and t.contno= b.contno and t.cvalidate<date'2025-07-22')  and b.CValiDate >=date'2016-02-18' and b.CValiDate <=date'2025-07-22'
//-- 新单计价成功才推送资产
and exists (select 1 from lcinsureacctrace where  busytype = 'NB' and edortype is null and moneytype = 'BF' and contno = b.contno and state = '1')

union
select * from lcpol b where
//--险种结算日期小于当前日期
exists (select 'X' from lcinsureacc a where b.polno = a.polno  and a.baladate<date'2025-07-22')
//--保单险种无效
and b.riskcode in ('413205','413101')
//-- 网销线上保单
and exists(select 1 from lccont t where t.SaleChnl='04' and t.selltype='20' and t.contno= b.contno)
//--保单终止
and (exists (select 'X' from lccontstate h where h.statetype in ('Available') and h.state = '1'  and b.contno =h.contno and h.polno = b.polno and h.enddate is null and b.appflag = '1' and  h.makedate = date'2025-07-22' )
or exists (select 'X' from lccontstate h where h.statetype in ('Terminate') and h.state = '1' and h.enddate is null  and b.appflag = '4' and b.contno =h.contno and h.polno = b.polno and  h.makedate = date'2025-07-22'))
and b.CValiDate >=date'2016-02-18' and b.CValiDate <=date'2025-07-22'
//-- modify by zsh 1120 排除掉陆金所成功申请线上保全犹退和保全退保业务的保单
and not exists(select 1 from  lpedoritem g,lpedorapp j where g.contno=b.contno and g.edoracceptno=j.edoracceptno  
and g.edortype in('WT','CT')  
and exists (select 1 from ldcode ld where ld.codetype='edorapptype' and ld.code=j.apptype and trim(ld.code)='46')
and exists(select 1 from  lccont lc where lc.contno=g.contno and lc.salechnl='04' and lc.selltype='56'))


5.1 保全队列梳理
5.1.1 支付宝队列处理
核心批处理：ZFBQueueDealTask

涉及主要表：ldcode1 ，lpedorapptemp ，lccont ，lcpol
--支付宝队列抽数逻辑
-- 1.配置，为抽数sql提供参数
SELECT code,othersign,code1 FROM ldcode1 WHERE codetype = 'ebsMove'；
-- 2.抽数sql
select distinct a.contno from lpedorapptemp a where a.state = '0'
/*按照保单尾号分线程*/
-- and substr(a.contno,20,1) in ("+tLastNumber+")
/*配置参数*/
AND EXISTS (SELECT * FROM lccont n,lcpol m WHERE n.contno = m.contno and n.contno = a.contno AND n.salechnl = '"+str_SaleChnl+"' and n.selltype = '"+str_SellType+"' and m.standbyflag2 in ("+str_Sku+"));


     5.1.2 投连出队列（非支付宝T+2）
核心批处理 ：投连出队列 QueueDealTask

涉及主要表：ldcode1 ，lpedorapptemp ，lccont ，lcpol
--（非支付宝T+2）抽数逻辑
select distinct a.contno from lpedorapptemp a where a.state = '0'
/*按照保单尾号分线程*/
-- and substr(a.contno,20,1) in ("+tLastNumber+")
and not exists (SELECT 1 FROM lccont n, lcpol m WHERE n.contno = m.contno and n.contno = a.contno and exists (select 1 from ldcode1 h where h.codetype = 'ebsMove' AND n.salechnl = h.othersign and n.selltype = h.code and m.standbyflag2 = replace(h.code1,'''','')))；


链接文档
保单监控系统文档
https://doc.weixin.qq.com/sheet/e3_AacA9gYiAEsCN6zsdtf8gQAmZw0aq?scode=ADUAZwe5ADsYGOjVw7AecA3ga9AC0&tab=BB08J2
添加注释
描述在文档里添加注释的方法，可使用高亮块的方式进行标注

相关参考文档
此处插入相关的编写文档
https://doc.weixin.qq.com/doc/w3_AXsAhgZAAKYFqJBvoR4Tv0fRArpwN?scode=AFAAJQcpAB4n5wTrfzAXsAhgZAAKY
https://doc.weixin.qq.com/doc/w3_AXsAhgZAAKYJvi9onhqSQyHq5Y2BQ?scode=AFAAJQcpAB4chV6Fm1AXsAhgZAAKY

联系我们
如有其它问题，可联系文档编写人员@邱中会


