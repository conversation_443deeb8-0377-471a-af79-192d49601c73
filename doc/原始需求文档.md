# 汇易达保单监控系统详细需求文档

## 1. 项目概述

### 1.1 项目背景

在数字化业务场景中，数据流动贯穿于各个环节，从保单生成到后续的计价、计息、风保扣费、资产推送等操作，数据的准确传输和处理至关重要。为保障数据在业务流程中的正常流转，及时发现异常，需要建设一个保单级数据流监控系统。

### 1.2 项目目标

本系统旨在监控所有投连保单每日的变化情况，及时发现异常并报警，通过可视化界面展示每个保单数据流详情，为运维提供监控数据支撑。

## 2. 系统功能需求

### 2.1 核心功能

#### 2.1.1 实时监控
- 保单计价状态监控
- 风险保费扣除状态监控
- 贷款结息状态监控
- 资产推送状态监控

#### 2.1.2 数据统计
- 提供保单数据的汇总与分析
- 支持按渠道、产品、保单状态等维度进行统计
- 提供历史数据查询功能

#### 2.1.3 预警通知
- 异常流程自动预警
- 支持多种预警方式（邮件、短信等）
- 提供预警历史记录查询

#### 2.1.4 权限管理
- 基于RBAC模型的用户权限控制
- 支持菜单和按钮级权限控制
- 提供用户、角色、部门管理功能

### 2.2 辅助功能

#### 2.2.1 定时任务
- 支持动态管理定时任务
- 提供任务执行历史记录查询
- 支持任务手动触发和暂停

#### 2.2.2 代码生成
- 根据数据库表结构自动生成CRUD代码
- 支持前后端代码生成
- 提供代码模板自定义功能

## 3. 数据库设计

### 3.1 概述

本文档根据init_ddl.sql文件整理了汇易达保单监控系统的所有数据库表结构，包括业务表和系统管理表。所有表均基于StarRocks数据库设计，采用OLAP引擎，支持分区和分桶策略以优化查询性能。

### 3.2 业务表结构

#### 3.2.1 资产文件明细表 (asset_file_detail)

用于存储从各渠道获取的资产文件详细信息，按监控日期进行分区。

| 字段名                | 数据类型           | 是否为空       | 描述                  |
|--------------------|----------------|------------|---------------------|
| id                 | bigint(20)     | NOT NULL   | 自增序号，主键             |
| monitor_date       | date           | NULL       | 监控日期                |
| asset_file_path    | varchar(500)   | NULL       | 资产文件路径              |
| asset_file_name    | varchar(200)   | NULL       | 资产文件名               |
| file_create_date   | date           | NULL       | 文件生成日期              |
| policy_no          | varchar(20)    | NULL       | 保单号                 |
| account_value      | decimal(18, 2) | NULL       | 账户价值（保留 2 位小数，单位：元） |
| accumulated_income | decimal(18, 2) | NULL       | 累计收益（保留 2 位小数，单位：元） |
| daily_income       | decimal(18, 2) | NULL       | 每日收益（保留 2 位小数，单位：元） |
| update_time        | datetime       | NULL       | 更新时间，配置最后一次修改的时间    |

**表属性**：
- 分区策略：按监控日期范围分区
- 分桶策略：按保单号哈希分桶，16个桶
- 副本数：3

#### 3.2.2 配置信息表 (config_info)

用于存储系统各种配置信息。

| 字段名          | 数据类型         | 是否为空       | 描述                 |
|--------------|--------------|------------|--------------------|
| id           | int(11)      | NOT NULL   | 自增序号，主键            |
| config_type  | varchar(50)  | NULL       | 配置类型               |
| config_def   | varchar(10)  | NULL       | 配置定义               |
| config_value | varchar(100) | NULL       | 配置值                |
| config_desc  | varchar(100) | NULL       | 配置说明               |
| remark       | varchar(100) | NULL       | 备注，记录配置的特殊说明       |
| status       | int(11)      | NULL       | 配置状态：0 = 启用，1 = 禁用 |
| create_time  | datetime     | NULL       | 创建时间（默认当前时间）       |
| update_time  | datetime     | NULL       | 更新时间，配置最后一次修改的时间   |

**表属性**：
- 副本数：3

#### 3.2.3 FTP信息表 (ftp_info)

用于存储FTP连接相关信息。

| 字段名              | 数据类型         | 是否为空      | 描述                       |
|------------------|--------------|-----------|--------------------------|
| id               | int(11)      | NOT NULL  | 自增序号，主键                  |
| file_type        | int(11)      | NULL      | 文件落地方式：0 = 本地存储，1=FTP 存储 |
| ftp_name         | varchar(100) | NULL      | FTP名称                    |
| description      | varchar(200) | NULL      | 描述                       |
| ftp_credential   | varchar(10)  | NULL      | FTP 认证类型：P = 密码 ，K = 密钥  |
| ftp_host         | varchar(50)  | NULL      | FTP 地址                   |
| ftp_port         | int(11)      | NULL      | FTP 端口                   |
| ftp_username     | varchar(50)  | NULL      | FTP 用户名                  |
| ftp_password     | varchar(50)  | NULL      | FTP 密码                   |
| private_key_path | varchar(200) | NULL      | 私钥路径                     |
| status           | int(11)      | NULL      | 状态：0=停用，1=启用             |
| file_path        | varchar(200) | NULL      | 文件路径                     |
| file_name        | varchar(100) | NULL      | 文件名 多个文件后缀不一样，用*表示       |
| create_time      | datetime     | NULL      | 创建时间（默认当前时间）             |
| update_time      | datetime     | NULL      | 更新时间，配置最后一次修改的时间         |

**表属性**：
- 副本数：3


#### 3.2.4 保单监控表 (policy_monitor)

用于存储保单监控基本信息。

| 字段名           | 数据类型        | 是否为空      | 描述      |
|---------------|-------------|-----------|---------|
| id            | int(11)     | NOT NULL  | 自增序号，主键 |
| monitor_date  | date        | NULL      | 监控日期    |
| policy_no     | varchar(20) | NULL      | 保单号     |
| policy_status | varchar(10) | NULL      | 保单状态    |

**表属性**：
- 副本数：3

#### 3.2.5 保单监控明细表 (policy_monitor_detail)

用于存储保单监控的详细信息，按监控日期进行分区。

| 字段名                                               | 数据类型           | 是否为空      | 描述                                      |
|---------------------------------------------------|----------------|-----------|-----------------------------------------|
| id                                                | int(11)        | NOT NULL  | 自增序号，主键                                 |
| monitor_date                                      | date           | NULL      | 监控日期                                    |
| policy_no                                         | varchar(20)    | NULL      | 保单号                                     |
| policy_system                                     | varchar(20)    | NULL      | 保单归属系统                                  |
| sales_channel                                     | varchar(50)    | NULL      | 销售渠道                                    |
| product_code                                      | varchar(20)    | NULL      | 产品编码                                    |
| product_name                                      | varchar(100)   | NULL      | 产品名称                                    |
| policy_status                                     | int(11)        | NULL      | 保单状态：0 = 有效，1 = 暂停，2 = 失效               |
| new_policy_core_status                            | int(11)        | NULL      | 新单进核心状态：0 = 已进核心，1 = 未进核心，2 = 不涉及"      |
| policy_change_core_status                         | int(11)        | NULL      | 保全进核心状态：0 = 已进核心，1 = 未进核心，2 = 不涉及"      |
| policy_change_queue_status                        | int(11)        | NULL      | 保全出队列状态：0 = 已出队列，1 = 未出队列，2 = 不涉及"      |
| pricing_status                                    | int(11)        | NULL      | 计价状态：0 = 已计价，1 = 未计价，2 = 不涉及            |
| pricing_date                                      | date           | NULL      | 计价日期，最后一次成功计价的日期（未计价或无需计价则为 NULL）       |
| risk_insurance_status                             | int(11)        | NULL      | 风险保费状态：0 = 已扣除，1 = 未扣除，2 = 不涉及          |
| risk_insurance_date                               | date           | NULL      | 风险保费扣除日期，最后一次成功扣除的日期（未扣除或无需扣除则为 NULL）   |
| interest_settlement_status                        | int(11)        | NULL      | 结息状态：0 = 已结息，1 = 未结息，2 = 不涉及            |
| interest_settlement_date                          | date           | NULL      | 结息日期，最后一次成功结息的日期（未结息或无需结息则为 NULL）       |
| asset_push_website_status                         | int(11)        | NULL      | 资产推送官网状态：0 = 已推送，1 = 未推送，2 = 不涉及        |
| asset_push_website_date                           | date           | NULL      | 资产推送官网日期，最后一次成功推送资产的日期（未推送或无需推送则为 NULL） |
| website_account_value                             | decimal(18, 2) | NULL      | 资产推送官网账户价值（保留 2 位小数，单位：元）               |
| website_accumulated_income                        | decimal(18, 2) | NULL      | 资产推送官网累计收益（保留 2 位小数，单位：元）               |
| website_daily_income                              | decimal(18, 2) | NULL      | 资产推送官网每日收益（保留 2 位小数，单位：元）               |
| asset_push_microsite_status                       | int(11)        | NULL      | 资产推送官微状态：0 = 已推送，1 = 未推送，2 = 不涉及        |
| asset_push_microsite_date                         | date           | NULL      | 资产推送官微日期，最后一次成功推送资产的日期（未推送或无需推送则为 NULL） |
| microsite_account_value                           | decimal(18, 2) | NULL      | 资产推送官微账户价值（保留 2 位小数，单位：元）               |
| microsite_accumulated_income                      | decimal(18, 2) | NULL      | 资产推送官微累计收益（保留 2 位小数，单位：元）               |
| microsite_daily_income                            | decimal(18, 2) | NULL      | 资产推送官微每日收益（保留 2 位小数，单位：元）               |
| asset_push_trade_status                           | int(11)        | NULL      | 资产推送交易状态：0 = 已推送，1 = 未推送，2 = 不涉及        |
| asset_push_trade_date                             | date           | NULL      | 资产推送交易日期，最后一次成功推送资产的日期（未推送或无需推送则为 NULL） |
| trade_account_value                               | decimal(18, 2) | NULL      | 资产推送交易账户价值（保留 2 位小数，单位：元）               |
| trade_accumulated_income                          | decimal(18, 2) | NULL      | 资产推送交易累计收益（保留 2 位小数，单位：元）               |
| trade_daily_income                                | decimal(18, 2) | NULL      | 资产推送交易每日收益（保留 2 位小数，单位：元）               |
| asset_push_channel_method                         | int(11)        | NULL      | 资产推送渠道方式，0 = 文件，1 = 接口                  |
| asset_push_channel_status                         | int(11)        | NULL      | 资产推送渠道状态：0 = 已推送，1 = 未推送，2 = 不涉及        |
| asset_push_channel_date                           | date           | NULL      | 资产推送渠道日期，最后一次成功推送资产的日期（未推送或无需推送则为 NULL） |
| channel_account_value                             | decimal(18, 2) | NULL      | 资产推送渠道账户价值（保留 2 位小数，单位：元）               |
| channel_accumulated_income                        | decimal(18, 2) | NULL      | 资产推送渠道累计收益（保留 2 位小数，单位：元）               |
| channel_daily_income                              | decimal(18, 2) | NULL      | 资产推送渠道每日收益（保留 2 位小数，单位：元）               |
| create_time                                       | datetime       | NULL      | 创建时间（默认当前时间）                            |
| update_time                                       | datetime       | NULL      | 更新时间，配置最后一次修改的时间                        |

**表属性**：
- 分区策略：按监控日期范围分区
- 分桶策略：按保单号哈希分桶，32个桶
- 副本数：3

#### 3.2.6 监控汇总结果表 (monitor_summary_result)

用于存储每日监控的汇总结果。

| 字段名                                      | 数据类型      | 是否为空      | 描述         |
|------------------------------------------|-----------|-----------|------------|
| id                                       | int(11)   | NOT NULL  | 自增序号，主键    |
| monitor_date                             | date      | NULL      | 监控日期       |
| total_policies                           | int(11)   | NULL      | 总保单数       |
| pricing_total                            | int(11)   | NULL      | 计价总数       |
| pricing_success                          | int(11)   | NULL      | 计价成功数      |
| pricing_failed                           | int(11)   | NULL      | 计价失败数      |
| pricing_not_applicable                   | int(11)   | NULL      | 计价不涉及数     |
| risk_insurance_total                     | int(11)   | NULL      | 风险保费总数     |
| risk_insurance_success                   | int(11)   | NULL      | 风险保费成功数    |
| risk_insurance_failed                    | int(11)   | NULL      | 风险保费失败数    |
| risk_insurance_not_applicable            | int(11)   | NULL      | 风险保费不涉及数   |
| interest_total                           | int(11)   | NULL      | 结息总数       |
| interest_success                         | int(11)   | NULL      | 结息成功数      |
| interest_failed                          | int(11)   | NULL      | 结息失败数      |
| interest_not_applicable                  | int(11)   | NULL      | 结息不涉及数     |
| asset_push_website_total                 | int(11)   | NULL      | 资产推送官网总数   |
| asset_push_website_success               | int(11)   | NULL      | 资产推送官网成功数  |
| asset_push_website_failed                | int(11)   | NULL      | 资产推送官网失败数  |
| asset_push_website_not_applicable        | int(11)   | NULL      | 资产推送官网不涉及数 |
| asset_push_microsite_total               | int(11)   | NULL      | 资产推送官微总数   |
| asset_push_microsite_success             | int(11)   | NULL      | 资产推送官微成功数  |
| asset_push_microsite_failed              | int(11)   | NULL      | 资产推送官微失败数  |
| asset_push_microsite_not_applicable      | int(11)   | NULL      | 资产推送官微不涉及数 |
| asset_push_trade_total                   | int(11)   | NULL      | 资产推送交易总数   |
| asset_push_trade_success                 | int(11)   | NULL      | 资产推送交易成功数  |
| asset_push_trade_failed                  | int(11)   | NULL      | 资产推送交易失败数  |
| asset_push_trade_not_applicable          | int(11)   | NULL      | 资产推送交易不涉及数 |
| asset_push_channel_total                 | int(11)   | NULL      | 资产推送渠道总数   |
| asset_push_channel_success               | int(11)   | NULL      | 资产推送渠道成功数  |
| asset_push_channel_failed                | int(11)   | NULL      | 资产推送渠道失败数  |
| asset_push_channel_not_applicable        | int(11)   | NULL      | 资产推送渠道不涉及数 |
| create_time                              | datetime  | NULL      | 创建时间       |
| update_time                              | datetime  | NULL      | 更新时间       |

**表属性**：
- 副本数：3

### 3.3 系统管理表结构

#### 3.3.1 批处理任务配置表 (batch_task_config)

用于配置和管理系统中的批处理任务。

| 字段名             | 数据类型           | 是否为空      | 描述               |
|-----------------|----------------|-----------|------------------|
| id              | int(11)        | NOT NULL  | 自增序号，主键          |
| task_name       | varchar(100)   | NULL      | 任务名称             |
| task_class      | varchar(200)   | NULL      | 任务类名             |
| cron_expression | varchar(50)    | NULL      | Cron表达式          |
| task_params     | varchar(65533) | NULL      | 任务参数             |
| task_status     | int(11)        | NULL      | 任务状态：0=停用，1=启用   |
| description     | varchar(500)   | NULL      | 任务描述             |
| create_time     | datetime       | NULL      | 创建时间（默认当前时间）     |
| update_time     | datetime       | NULL      | 更新时间，配置最后一次修改的时间 |

**表属性**：
- 副本数：3

#### 3.3.2 批处理任务日志表 (batch_task_log)

用于记录批处理任务的执行日志。

| 字段名            | 数据类型           | 是否为空       | 描述                   |
|----------------|----------------|------------|----------------------|
| id             | bigint(20)     | NOT NULL   | 自增序号，主键              |
| task_id        | bigint(20)     | NULL       | 任务ID                 |
| task_name      | varchar(100)   | NULL       | 任务名称                 |
| start_time     | datetime       | NULL       | 开始时间                 |
| end_time       | datetime       | NULL       | 结束时间                 |
| execution_time | bigint(20)     | NULL       | 执行时长（毫秒）             |
| task_status    | int(11)        | NULL       | 执行状态：0=失败，1=成功，2=执行中 |
| result_message | varchar(65533) | NULL       | 执行结果信息               |
| error_message  | varchar(65533) | NULL       | 错误信息                 |
| create_time    | datetime       | NULL       | 创建时间（默认当前时间）         |

**表属性**：
- 副本数：3

#### 3.3.3 部门表 (sys_dept)

用于存储系统组织架构中的部门信息。

| 字段名         | 数据类型        | 是否为空      | 描述                |
|-------------|-------------|-----------|-------------------|
| dept_id     | bigint(20)  | NOT NULL  | 部门ID              |
| parent_id   | bigint(20)  | NULL      | 父部门id             |
| ancestors   | varchar(50) | NULL      | 祖级列表              |
| dept_name   | varchar(30) | NULL      | 部门名称              |
| order_num   | int(11)     | NULL      | 显示顺序              |
| leader      | varchar(20) | NULL      | 负责人               |
| phone       | varchar(11) | NULL      | 联系电话              |
| email       | varchar(50) | NULL      | 邮箱                |
| status      | char(1)     | NULL      | 部门状态（0正常 1停用）     |
| del_flag    | char(1)     | NULL      | 删除标志（0代表存在 2代表删除） |
| create_time | datetime    | NULL      | 创建时间              |
| update_time | datetime    | NULL      | 更新时间              |

**表属性**：
- 副本数：3

#### 3.3.4 登录日志表 (sys_logininfor)

用于记录用户登录系统的信息。

| 字段名            | 数据类型         | 是否为空      | 描述            |
|----------------|--------------|-----------|---------------|
| info_id        | bigint(20)   | NOT NULL  | 日志ID          |
| user_name      | varchar(50)  | NULL      | 用户账号          |
| ipaddr         | varchar(128) | NULL      | 登录IP地址        |
| login_location | varchar(255) | NULL      | 登录地点          |
| browser        | varchar(50)  | NULL      | 浏览器类型         |
| os             | varchar(50)  | NULL      | 操作系统          |
| status         | char(1)      | NULL      | 登录状态（0成功 1失败） |
| msg            | varchar(255) | NULL      | 提示消息          |
| login_time     | datetime     | NULL      | 访问时间          |

**表属性**：
- 副本数：3

#### 3.3.5 菜单权限表 (sys_menu)

用于存储系统菜单和权限信息。

| 字段名         | 数据类型         | 是否为空      | 描述                |
|-------------|--------------|-----------|-------------------|
| menu_id     | bigint(20)   | NOT NULL  | 菜单ID              |
| menu_name   | varchar(50)  | NOT NULL  | 菜单名称              |
| parent_id   | bigint(20)   | NULL      | 父菜单ID             |
| order_num   | int(11)      | NULL      | 显示顺序              |
| path        | varchar(200) | NULL      | 路由地址              |
| component   | varchar(255) | NULL      | 组件路径              |
| query       | varchar(255) | NULL      | 路由参数              |
| is_frame    | int(11)      | NULL      | 是否为外链（0是 1否）      |
| is_cache    | int(11)      | NULL      | 是否缓存（0缓存 1不缓存）    |
| menu_type   | char(1)      | NULL      | 菜单类型（M目录 C菜单 F按钮） |
| visible     | char(1)      | NULL      | 菜单状态（0显示 1隐藏）     |
| status      | char(1)      | NULL      | 菜单状态（0正常 1停用）     |
| perms       | varchar(100) | NULL      | 权限标识              |
| icon        | varchar(100) | NULL      | 菜单图标              |
| create_time | datetime     | NULL      | 创建时间              |
| update_time | datetime     | NULL      | 更新时间              |
| remark      | varchar(500) | NULL      | 备注                |

**表属性**：
- 副本数：3

#### 3.3.6 操作日志表 (sys_oper_log)

用于记录用户在系统中的操作日志。

| 字段名            | 数据类型          | 是否为空      | 描述                     |
|----------------|---------------|-----------|------------------------|
| oper_id        | bigint(20)    | NOT NULL  | 日志ID                   |
| title          | varchar(50)   | NULL      | 模块标题                   |
| business_type  | int(11)       | NULL      | 业务类型（0其它 1新增 2修改 3删除）  |
| method         | varchar(100)  | NULL      | 方法名称                   |
| request_method | varchar(10)   | NULL      | 请求方式                   |
| operator_type  | int(11)       | NULL      | 操作类别（0其它 1后台用户 2手机端用户） |
| oper_name      | varchar(50)   | NULL      | 操作人员                   |
| dept_name      | varchar(50)   | NULL      | 部门名称                   |
| oper_url       | varchar(255)  | NULL      | 请求URL                  |
| oper_ip        | varchar(128)  | NULL      | 主机地址                   |
| oper_location  | varchar(255)  | NULL      | 操作地点                   |
| oper_param     | varchar(2000) | NULL      | 请求参数                   |
| json_result    | varchar(2000) | NULL      | 返回参数                   |
| status         | int(11)       | NULL      | 操作状态（0正常 1异常）          |
| error_msg      | varchar(2000) | NULL      | 错误消息                   |
| oper_time      | datetime      | NULL      | 操作时间                   |

**表属性**：
- 副本数：3

#### 3.3.7 角色信息表 (sys_role)

用于存储系统中的角色信息。

| 字段名                 | 数据类型         | 是否为空      | 描述                                             |
|---------------------|--------------|-----------|------------------------------------------------|
| role_id             | bigint(20)   | NOT NULL  | 角色ID                                           |
| role_name           | varchar(30)  | NOT NULL  | 角色名称                                           |
| role_key            | varchar(100) | NOT NULL  | 角色权限字符串                                        |
| role_sort           | int(11)      | NOT NULL  | 显示顺序                                           |
| data_scope          | char(1)      | NULL      | 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限） |
| menu_check_strictly | tinyint(4)   | NULL      | 菜单树选择项是否关联显示                                   |
| dept_check_strictly | tinyint(4)   | NULL      | 部门树选择项是否关联显示                                   |
| status              | char(1)      | NOT NULL  | 角色状态（0正常 1停用）                                  |
| del_flag            | char(1)      | NULL      | 删除标志（0代表存在 2代表删除）                              |
| create_time         | datetime     | NULL      | 创建时间                                           |
| update_time         | datetime     | NULL      | 更新时间                                           |
| remark              | varchar(500) | NULL      | 备注                                             |

**表属性**：
- 副本数：3

#### 3.3.8 角色部门关联表 (sys_role_dept)

用于存储角色和部门的关联关系。

| 字段名     | 数据类型       | 是否为空      | 描述    |
|---------|------------|-----------|-------|
| role_id | bigint(20) | NOT NULL  | 角色ID  |
| dept_id | bigint(20) | NOT NULL  | 部门ID  |

**表属性**：
- 副本数：3

#### 3.3.9 角色菜单关联表 (sys_role_menu)

用于存储角色和菜单的关联关系。

| 字段名     | 数据类型       | 是否为空      | 描述    |
|---------|------------|-----------|-------|
| role_id | bigint(20) | NOT NULL  | 角色ID  |
| menu_id | bigint(20) | NOT NULL  | 菜单ID  |

**表属性**：
- 副本数：3

#### 3.3.10 用户信息表 (sys_user)

用于存储系统用户信息。

| 字段名          | 数据类型         | 是否为空      | 描述                |
|--------------|--------------|-----------|-------------------|
| user_id      | bigint(20)   | NOT NULL  | 用户ID，主键           |
| dept_id      | bigint(20)   | NULL      | 部门ID              |
| user_name    | varchar(30)  | NOT NULL  | 用户账号              |
| nick_name    | varchar(30)  | NOT NULL  | 用户昵称              |
| user_type    | varchar(2)   | NULL      | 用户类型（00系统用户）      |
| email        | varchar(50)  | NULL      | 用户邮箱              |
| phone_number | varchar(11)  | NULL      | 手机号码              |
| sex          | char(1)      | NULL      | 用户性别（0男 1女）       |
| password     | varchar(100) | NULL      | 密码                |
| status       | char(1)      | NULL      | 帐号状态（0正常 1停用）     |
| del_flag     | char(1)      | NULL      | 删除标志（0代表存在 1代表删除） |

**表属性**：
- 副本数：3

#### 3.3.11 用户角色关联表 (sys_user_role)

用于存储用户和角色的关联关系。

| 字段名     | 数据类型       | 是否为空      | 描述    |
|---------|------------|-----------|-------|
| user_id | bigint(20) | NOT NULL  | 用户ID  |
| role_id | bigint(20) | NOT NULL  | 角色ID  |

**表属性**：
- 副本数：3

## 4. 功能详细设计

### 4.1 字段说明

#### 基础字段
- **监控日期**：默认系统当前日期
- **保单号**：以核心系统为基础，关联交易中心为进核心数据，关联阿里云未进核心数据，保单来源应为阿里云(阿里云暂无新单)
- **保单来源系统**：需各业务系统打系统标签，如交易中心、阿里云等，未打标签的默认核心系统
- **销售渠道**：以各业务系统记录为准
- **产品编码**：以各业务系统记录为准
- **产品名称**：以各业务系统记录为准，显示对外包装的名称如小金罐、小金余
- **保单状态**：以核心系统为准
- **新单进核心状态**：保单归属系统是交易的，以交易为准，归属阿里云以核心为准
- **保全进核心状态**：保单归属系统是交易的，以交易为准，归属阿里云以核心为准
- **保全出队列状态**：以核心系统为准

#### 计价相关字段
- **计价逻辑说明**：根据保单来源系统取值，记录阿里云从阿里云系统取值，非阿里云都从核心取值
- **计价状态**：
  - 应计价条数=实际计价条数，显示"是"
  - 应计价条数≠实际计价条数，显示为"否"
  - 无应计价数据，显示"不涉及"
- **应计价条数**：与保单来源系统计价抽数逻辑保持一致
- **实际计价条数**：计价日期为当天的数据
- **计价日期**：系统记录的真实计价日期，当计价状态为"否"或"不涉及"为空

#### 风险保费相关字段
- **风险保费逻辑说明**：根据保单来源系统取值，记录阿里云从阿里云系统取值，非阿里云都从核心取值
- **风险保费状态**：
  - 应扣除风险保费条数=实际扣除风险保费条数，显示"是"
  - 应扣除风险保费条数≠实际扣除风险保费条数，显示"否"
  - 如不需要扣除风险保费，显示"不涉及"
- **应扣除风险保费条数**：与保单来源系统风险保费抽数逻辑保持一致
- **实际扣除风险保费条数**：风险保费扣除日期为当天的数据
- **风险保费扣除日期**：系统记录的真实风险保费扣除日期，当结息状态为"否"或"不涉及"为空

#### 贷款结息相关字段
- **贷款结息逻辑说明**：根据保单来源系统取值，记录阿里云从阿里云系统取值，非阿里云都从核心取值
- **贷款结息状态**：
  - 应结息条数=实际结息条数，显示"是"
  - 应结息条数≠实际结息条数，显示"否"
  - 没有需要结息的数据，显示"不涉及"
- **结息日期**：系统记录的结息日期，当结息状态为"否"或"不涉及"为空


#### 资产推送相关字段
- **资产推送逻辑说明**：根据保单来源系统取值，记录阿里云从阿里云系统取值，非阿里云都从核心取值
- **资产推送官网状态**：以官网数据库为准
  - 0 = 已推送资产
  - 1 = 未推送资产
  - 2 = 无需推送资产
- **资产推送官网日期**：系统记录的资产推送官网日期，当资产推送官网状态为"未推送资产"或"无需推送资产"时为空
- **资产推送官网账户价值**：官网数据库中记录的账户价值（保留2位小数，单位：元）
- **资产推送官网累计收益**：官网数据库中记录的累计收益（保留2位小数，单位：元）
- **资产推送官网每日收益**：官网数据库中记录的每日收益（保留2位小数，单位：元）
- **资产推送官微状态**：以官微数据库为准
  - 0 = 已推送资产
  - 1 = 未推送资产
  - 2 = 无需推送资产
- **资产推送官微日期**：系统记录的资产推送官微日期，当资产推送官微状态为"未推送资产"或"无需推送资产"时为空
- **资产推送官微账户价值**：官微数据库中记录的账户价值（保留2位小数，单位：元）
- **资产推送官微累计收益**：官微数据库中记录的的累计收益（保留2位小数，单位：元）
- **资产推送官微每日收益**：官微数据库中记录的每日收益（保留2位小数，单位：元）
- **资产推送交易状态**：以交易中心数据库为准
  - 0 = 已推送资产
  - 1 = 未推送资产
  - 2 = 无需推送资产
- **资产推送交易日期**：系统记录的资产推送交易日期，当资产推送交易状态为"未推送资产"或"无需推送资产"时为空
- **资产推送交易账户价值**：交易中心库记录的账户价值（保留2位小数，单位：元）
- **资产推送交易累计收益**：交易中心库记录的累计收益（保留2位小数，单位：元）
- **资产推送交易每日收益**：交易中心库记录的每日收益（保留2位小数，单位：元）
- **资产推送渠道方式**：默认文件方式推送
  - 0 = 文件方式推送
  - 1 = 接口方式推送
- **资产推送渠道状态**：以给渠道的资产文件为准
  - 0 = 已推送资产
  - 1 = 未推送资产
  - 2 = 无需推送资产
- **资产推送渠道日期**：以给渠道的资产文件生成日期为准，当资产推送渠道状态为"未推送资产"或"无需推送资产"时为空
- **资产推送渠道账户价值**：从给渠道的资产文件中获取的账户价值（保留2位小数，单位：元）
- **资产推送渠道累计收益**：从给渠道的资产文件中获取的累计收益（保留2位小数，单位：元）
- **资产推送渠道每日收益**：从给渠道的资产文件中获取的每日收益（保留2位小数，单位：元）

### 4.2 功能模块设计

#### 4.2.1 配置管理模块
- 定义各种状态如保单状态、计价状态、风保状态结息状态
- 支持页面增删改查
- 支持导入导出

#### 4.2.2 FTP配置管理模块
- 仅配置跟文件相关的信息，支持本地路径或sftp
- 支持页面增删改查
- 支持导入导出

#### 4.2.3 保单监控模块
- 全量投连保单监控
- 只保留当天数据

#### 4.2.4 保单监控明细模块
- 每日全量投连保单生产每日监控明细数据
- 默认显示当天数据，显示字段如下：监控日期、保单号、保单来源系统、销售渠道、产品名称、保单状态、新单进核心状态、保全进核心状态、保全出队列状态、计价状态、计价日期、风险保费扣除状态、风险保费扣除日期、贷款结息状态、贷款结息状态、资产推送官网状态、资产推送官网日期、资产推送官微状态、资产推送官微日期、资产推送交易状态、资产推送交易日期、资产推送渠道方式、资产推送渠道状态、资产推送渠道日期
- 提供明细查询和导出功能
- 支持用户自定义设置全表所有字段或者部分字段
- 所有字段都支持筛选

#### 4.2.5 文件读取模块
- 支持本地文件读取或sftp读取
- 支持密码或秘钥登录
- 支持遍历多文件下载并解析
- 支持解析失败重新触发

#### 4.2.6 批处理模块
- **资产文件解析批处理**：定时触发，根据ftp配置表相关路径下载文件到本地目录，解析文件并保存到数据库资产文件明细表中
- **保单监控明细批处理**：定时触发，根据资产文件明细表当日数据更新保单监控明细表资产推送渠道状态、资产推送渠道日期、资产推送渠道账户价值、资产推送渠道累计收益、资产推送渠道每日收益，汇总到监控汇总结果表中并发送企微消息通知

### 4.3 预警设计

#### 4.3.1 预警内容
每日定时发送监控汇总情况，只发送一次，格式如下：

**【2025-08-05日，投连保单监控结果如下】**

- **【计价监控】【成功/失败】【应计价保单XX条，未计价YY条】**
- **【风险保费监控】【成功/失败】【应扣除风险保费保单XX条，未扣YY条】**
- **【结息监控】【成功/失败】【应结息保单XX条，未结息YY条】**
- **【资产推送官网监控】【成功/失败】【资产应推送官网保单XX条，未推送YY条】**
- **【资产推送官微监控】【成功/失败】【资产应推送官微保单XX条，未推送YY条】**
- **【资产推送交易监控】【成功/失败】【资产应推送交易保单XX条，未推送YY条】**
- **【资产推送渠道监控】【成功/失败】【资产应推送渠道保单XX条，未推送YY条】**

#### 4.3.2 预警规则
- 所有监控按保单去重进行汇总
- XX=状态"是"+状态"否"
- YY=状态"否",若YY等于0，预警状态"成功"否则"失败"

## 5. 系统非功能需求

### 5.1 性能需求
- 支持高并发查询
- 大数据量下保持查询性能（通过分区和分桶优化）
- 页面响应时间不超过3秒

### 5.2 安全需求
- RBAC权限控制
- 用户登录认证（基于Spring Security）
- 操作日志记录
- 数据传输加密

### 5.3 可用性需求
- 系统可用性不低于99.9%
- 支持故障自动恢复
- 提供友好的用户界面

### 5.4 可维护性需求
- 模块化设计，便于维护和扩展
- 提供完善的日志记录
- 支持在线升级和配置修改

## 6. 系统架构设计

### 6.1 技术架构
- 前后端分离架构
- 后端基于Spring Boot + MyBatis + Spring Security + StarRocks
- 前端基于Vue.js + Element UI

### 6.2 部署架构
- 采用大数据的数据同步链路
- 从交易中心库、核心库、阿里云库同步需要的表到大数据中
- 在大数据中开发脚本进行数据加工，数据落starrocks库
- 需支持增量自动同步

## 7. 数据处理流程

### 7.1 数据同步流程
1. 从交易中心库、核心库、阿里云库同步需要的表到大数据中
2. 在大数据中开发脚本进行数据加工罗大数据本地
3. 大数据都加工完成后数据同步到starrocks库

## 8. 系统风险评估
1. 技术风险
- 数据同步失败：可能导致监控数据不准确
- 影响：监控数据不准确
- 缓解措施：增加数据同步监控，如支持可增加自动重试机制

- 系统性能瓶颈：数据量大导致无法在规定时间内计算完成
- 影响：数据滞后、监控不准确
- 缓解措施：性能监控必要时申请独享资源

## 9. 功能验收
- 数据同步功能正常运行
- 监控数据准确完整
- 预警功能及时有效

## 10. 部署需求

### 10.1 环境要求
- JDK 1.8+
- StarRocks 2.0+（兼容MySQL协议）
- Maven 3.0+
- Node.js 12.0+
- npm 6.0+

### 10.2 部署流程
1. 环境准备
2. 数据库初始化
3. 后端服务部署
4. 前端服务部署
5. 系统配置
6. 功能验证