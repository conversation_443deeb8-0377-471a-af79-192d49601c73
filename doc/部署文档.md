# 汇易达保单监控系统部署文档

## 1. 系统环境要求

### 1.1 软件环境

#### 后端环境要求
- **操作系统**：Linux CentOS 7.x 或 Windows Server 2016及以上
- **JDK版本**：JDK 1.8 或更高版本
- **数据库**：StarRocks 2.0+（兼容MySQL协议）
- **缓存数据库**：Redis 5.0+
- **构建工具**：Maven 3.0+
- **应用服务器**：Tomcat 8.5+（可选，应用内嵌Spring Boot）

#### 前端环境要求
- **Node.js**：12.0 或更高版本
- **npm**：6.0 或更高版本
- **构建工具**：Webpack 4.0+

### 1.2 硬件环境要求

#### 最低配置
- **CPU**：4核
- **内存**：8GB
- **硬盘**：100GB可用空间

#### 推荐配置
- **CPU**：8核
- **内存**：16GB
- **硬盘**：500GB可用空间（根据数据量调整）

## 2. 部署前准备

### 2.1 数据库准备

#### 2.1.1 StarRocks安装
1. 下载并安装StarRocks数据库
2. 创建数据库：
   ```sql
   CREATE DATABASE huida_monitor;
   ```
3. 执行数据库初始化脚本：
   ```bash
   mysql -u root -p huida_monitor < monit-backend/database/init_ddl.sql
   mysql -u root -p huida_monitor < monit-backend/database/init_dml.sql
   ```

#### 2.1.2 Redis安装
1. 下载并安装Redis 5.0+
2. 启动Redis服务
3. 配置Redis持久化策略

### 2.2 JDK安装配置

1. 下载并安装JDK 1.8+
2. 配置环境变量：
   ```bash
   export JAVA_HOME=/usr/java/jdk1.8.0_XXX
   export PATH=$PATH:$JAVA_HOME/bin
   ```

### 2.3 Maven安装配置

1. 下载并安装Maven 3.0+
2. 配置环境变量：
   ```bash
   export MAVEN_HOME=/opt/apache-maven-3.X.X
   export PATH=$PATH:$MAVEN_HOME/bin
   ```

### 2.4 Node.js安装配置

1. 下载并安装Node.js 12.0+
2. 验证安装：
   ```bash
   node --version
   npm --version
   ```

## 3. 后端服务部署

### 3.1 代码编译

1. 进入后端项目根目录：
   ```bash
   cd huida-monitor-platform
   ```

2. 执行Maven编译打包：
   ```bash
   mvn clean package -Dmaven.test.skip=true
   ```

3. 编译完成后，生成的jar包位于：
   ```
   monit-backend/huida-admin/target/huida-admin.jar
   ```

### 3.2 配置文件修改

1. 修改数据库配置文件：
   ```yaml
   # application.yml
   spring:
     datasource:
       url: **********************************************************************************************************************************************************
       username: 数据库用户名
       password: 数据库密码
       driver-class-name: com.mysql.cj.jdbc.Driver
   ```

2. 修改Redis配置：
   ```yaml
   # application.yml
   spring:
     redis:
       host: Redis服务器IP
       port: 6379
       password: Redis密码（如有）
   ```

### 3.3 服务启动

#### 3.3.1 直接启动
```bash
java -jar huida-admin.jar
```

#### 3.3.2 后台启动
```bash
nohup java -jar huida-admin.jar > huida.log 2>&1 &
```

#### 3.3.3 指定配置文件启动
```bash
java -jar huida-admin.jar --spring.profiles.active=prod
```

### 3.4 服务管理

#### 3.4.1 查看服务状态
```bash
ps -ef | grep huida-admin
```

#### 3.4.2 停止服务
```bash
kill -9 <进程ID>
```

## 4. 前端页面部署

### 4.1 依赖安装

1. 进入前端项目目录：
   ```bash
   cd monitor-front
   ```

2. 安装项目依赖：
   ```bash
   npm install
   ```

### 4.2 配置修改

1. 修改后端API地址配置：
   ```javascript
   // monitor-front/src/config/index.js
   const API_BASE_URL = 'http://后端服务地址:端口';
   ```

### 4.3 项目构建

1. 开发环境运行：
   ```bash
   npm run serve
   ```

2. 生产环境打包：
   ```bash
   npm run build
   ```

3. 构建完成后，生成的静态文件位于：
   ```
   monitor-front/dist/
   ```

### 4.4 静态文件部署

1. 将dist目录下的所有文件复制到Nginx静态目录：
   ```bash
   cp -r dist/* /usr/share/nginx/html/
   ```

2. 配置Nginx反向代理：
   ```nginx
   server {
       listen 80;
       server_name 域名或IP;
       
       location / {
           root /usr/share/nginx/html;
           index index.html;
           try_files $uri $uri/ /index.html;
       }
       
       location /api {
           proxy_pass http://后端服务地址:端口;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       }
   }
   ```

## 5. Nginx配置

### 5.1 安装Nginx

#### CentOS系统
```bash
yum install nginx -y
systemctl start nginx
systemctl enable nginx
```

#### Ubuntu系统
```bash
apt-get install nginx -y
systemctl start nginx
systemctl enable nginx
```

### 5.2 配置文件修改

编辑Nginx配置文件：
```bash
vim /etc/nginx/nginx.conf
```

添加或修改server配置：
```nginx
server {
    listen 80;
    server_name localhost;
    
    # 前端静态文件
    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
    }
    
    # 后端API代理
    location /api {
        proxy_pass http://127.0.0.1:8080;  # 后端服务地址
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    
    # 文件访问
    location /profile {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

### 5.3 重启Nginx服务
```bash
systemctl restart nginx
```

## 6. 系统初始化

### 6.1 访问系统

部署完成后，通过浏览器访问系统：
```
http://服务器IP或域名
```

默认登录账号密码：
- 用户名：admin
- 密码：admin123

### 6.2 系统配置

1. 登录系统后，进入"系统管理"->"参数配置"模块
2. 根据实际需求修改系统参数
3. 配置FTP信息（如需要）
4. 配置批处理任务

### 6.3 权限配置

1. 进入"系统管理"->"角色管理"模块
2. 根据业务需求配置角色权限
3. 进入"系统管理"->"用户管理"模块
4. 分配用户角色

## 7. 监控与维护

### 7.1 日志查看

#### 后端服务日志
```bash
tail -f huida.log
```

#### Nginx访问日志
```bash
tail -f /var/log/nginx/access.log
```

#### Nginx错误日志
```bash
tail -f /var/log/nginx/error.log
```

### 7.2 性能监控

1. 监控系统CPU、内存、磁盘使用情况
2. 监控数据库连接数和性能
3. 监控Redis内存使用情况
4. 监控应用服务响应时间

### 7.3 备份策略

#### 数据库备份
```bash
mysqldump -u 用户名 -p 数据库名 > backup_$(date +%Y%m%d).sql
```

#### 应用配置备份
```bash
tar -czf app_backup_$(date +%Y%m%d).tar.gz application.yml
```

## 8. 常见问题处理

### 8.1 服务无法启动

1. 检查端口是否被占用：
   ```bash
   netstat -tlnp | grep :8080
   ```

2. 检查数据库连接配置是否正确

3. 查看日志文件定位错误原因

### 8.2 页面无法访问

1. 检查Nginx配置是否正确
2. 检查后端服务是否正常运行
3. 检查防火墙设置

### 8.3 数据库连接失败

1. 检查数据库服务是否启动
2. 检查数据库连接配置
3. 检查网络连接是否正常
4. 检查数据库用户权限

## 9. 升级部署

### 9.1 后端服务升级

1. 停止当前服务
2. 备份当前jar包和配置文件
3. 部署新的jar包
4. 恢复配置文件（如有变更）
5. 启动服务

### 9.2 数据库升级

1. 对比新旧数据库结构
2. 备份当前数据库
3. 执行数据库变更脚本
4. 验证数据完整性

### 9.3 前端页面升级

1. 备份当前静态文件
2. 构建新的前端项目
3. 部署新的静态文件
4. 清除浏览器缓存