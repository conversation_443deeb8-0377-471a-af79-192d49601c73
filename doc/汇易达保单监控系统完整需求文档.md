# 汇易达保单监控系统完整需求文档

## 1. 项目概述

### 1.1 项目背景
在数字化业务场景中，数据流动贯穿于各个环节，从保单生成到后续的计价、计息、风保扣费、资产推送等操作，数据的准确传输和处理至关重要。为保障数据在业务流程中的正常流转，及时发现异常，需要建设一个保单级数据流监控系统。

### 1.2 项目目标
本系统旨在监控所有投连保单每日的变化情况，及时发现异常并报警，通过可视化界面展示每个保单数据流详情，为运维提供监控数据支撑。

### 1.3 技术架构
- **数据库**：StarRocks（兼容MySQL协议）
- **后端框架**：Spring Boot + MyBatis + Spring Security
- **前端框架**：Vue.js + Element UI（基于若依分离版）
- **权限管理**：基于RBAC模型的权限控制

## 2. 系统功能需求

### 2.1 核心业务功能

#### 2.1.1 保单监控管理
- **保单监控明细**：每日全量投连保单监控明细数据
- **监控汇总结果**：每日监控的汇总统计
- **实时监控**：保单计价、风险保费、贷款结息、资产推送状态监控
- **数据统计**：按渠道、产品、保单状态等维度进行统计分析

#### 2.1.2 配置管理
- **FTP配置管理**：支持本地路径或SFTP配置，密码或密钥认证
- **系统配置管理**：各种状态配置和系统参数配置
- **批处理任务配置**：定时任务的配置和管理

#### 2.1.3 文件处理
- **资产文件解析**：支持本地文件读取或SFTP读取
- **文件下载解析**：支持遍历多文件下载并解析
- **解析失败重试**：支持解析失败重新触发

#### 2.1.4 预警通知
- **异常流程自动预警**：监控异常自动报警
- **企微消息通知**：每日监控汇总结果推送
- **预警历史记录**：提供预警历史查询

### 2.2 系统管理功能

#### 2.2.1 用户权限管理（基于若依框架）
- **用户管理**：用户信息的增删改查
- **角色管理**：角色权限的配置和分配
- **部门管理**：组织架构的管理
- **菜单管理**：系统菜单和权限的配置
- **岗位管理**：岗位信息的管理

#### 2.2.2 系统监控
- **登录日志**：用户登录记录
- **操作日志**：用户操作行为记录
- **定时任务**：任务执行状态和历史记录
- **系统监控**：服务器性能监控

#### 2.2.3 系统工具
- **代码生成**：根据数据库表结构自动生成CRUD代码
- **系统接口**：Swagger API文档
- **数据字典**：系统字典数据管理

## 3. 数据库设计

### 3.1 设计原则
- 基于StarRocks数据库，采用OLAP引擎
- 支持分区和分桶策略优化查询性能
- 系统管理表以若依框架为准
- 业务表以详细需求文档为准
- 所有表设置副本数为3，保证高可用

### 3.2 业务表结构详细设计

#### 3.2.1 资产文件明细表 (asset_file_detail)

**表说明**：存储从各渠道获取的资产文件详细信息，按监控日期进行分区。

| 字段名                  | 数据类型             | 长度  | 是否为空 | 默认值 | 主键 | 索引 | 描述                                    |
|----------------------|------------------|-----|------|-----|----|----|---------------------------------------|
| id                   | bigint           | 20  | NO   | -   | PK | -  | 自增序号，主键                               |
| monitor_date         | date             | -   | YES  | -   | -  | IDX| 监控日期，用于分区                             |
| asset_file_path      | varchar          | 500 | YES  | -   | -  | -  | 资产文件完整路径                              |
| asset_file_name      | varchar          | 200 | YES  | -   | -  | IDX| 资产文件名称                                |
| file_create_date     | date             | -   | YES  | -   | -  | IDX| 文件生成日期                                |
| policy_no            | varchar          | 20  | YES  | -   | -  | IDX| 保单号，关联保单监控明细表                         |
| account_value        | decimal          | 18,2| YES  | 0.00| -  | -  | 账户价值（保留2位小数，单位：元）                     |
| accumulated_income   | decimal          | 18,2| YES  | 0.00| -  | -  | 累计收益（保留2位小数，单位：元）                     |
| daily_income         | decimal          | 18,2| YES  | 0.00| -  | -  | 每日收益（保留2位小数，单位：元）                     |
| daily_interest       | decimal          | 18,2| YES  | 0.00| -  | -  | 当日利息（保留2位小数，单位：元）                     |
| accumulated_interest | decimal          | 18,2| YES  | 0.00| -  | -  | 累计利息（保留2位小数，单位：元）                     |
| bonus_income         | decimal          | 18,2| YES  | 0.00| -  | -  | 加息收益（保留2位小数，单位：元）                     |
| create_time          | datetime         | -   | YES  | NOW()| -  | -  | 创建时间，默认系统时间                           |
| update_time          | datetime         | -   | YES  | NOW()| -  | -  | 更新时间，配置最后一次修改的时间                      |

**表属性**：
- 存储引擎：StarRocks OLAP
- 分区策略：按监控日期范围分区 `PARTITION BY RANGE(monitor_date)`
- 分桶策略：按保单号哈希分桶，16个桶 `DISTRIBUTED BY HASH(policy_no) BUCKETS 16`
- 副本数：3
- 数据保留：建议保留1年历史数据

**业务规则**：
- 每日批处理任务解析资产文件后插入数据
- 同一保单同一日期可能有多条记录（来自不同文件）
- 金额字段精度为2位小数，支持负数
- 文件路径和文件名用于追溯数据来源

#### 3.2.2 配置信息表 (config_info)

**表说明**：存储系统各种配置信息和参数，支持动态配置管理。

| 字段名          | 数据类型    | 长度  | 是否为空 | 默认值 | 主键 | 索引 | 描述                                      |
|--------------|---------|-----|------|-----|----|----|-------------------------------------------|
| id           | int     | 11  | NO   | -   | PK | -  | 自增序号，主键                                 |
| config_type  | varchar | 50  | YES  | -   | -  | IDX| 配置类型（如：POLICY_STATUS、PRICING_STATUS等）  |
| config_def   | varchar | 10  | YES  | -   | -  | IDX| 配置定义代码                                  |
| config_value | varchar | 100 | YES  | -   | -  | -  | 配置值                                     |
| config_desc  | varchar | 100 | YES  | -   | -  | -  | 配置说明                                    |
| remark       | varchar | 100 | YES  | -   | -  | -  | 备注，记录配置的特殊说明                            |
| status       | int     | 11  | YES  | 0   | -  | -  | 配置状态：0=启用，1=禁用                          |
| create_time  | datetime| -   | YES  | NOW()| -  | -  | 创建时间（默认当前时间）                            |
| update_time  | datetime| -   | YES  | NOW()| -  | -  | 更新时间，配置最后一次修改的时间                        |

**表属性**：
- 存储引擎：StarRocks OLAP
- 分桶策略：按ID哈希分桶，1个桶
- 副本数：3

**配置类型说明**：
- `POLICY_STATUS`：保单状态配置（0=有效，1=暂停，2=失效）
- `PRICING_STATUS`：计价状态配置（0=已计价，1=未计价，2=不涉及）
- `RISK_INSURANCE_STATUS`：风险保费状态配置（0=已扣除，1=未扣除，2=不涉及）
- `INTEREST_STATUS`：结息状态配置（0=已结息，1=未结息，2=不涉及）
- `PUSH_STATUS`：推送状态配置（0=已推送，1=未推送，2=不涉及）
- `SYSTEM_PARAM`：系统参数配置

#### 3.2.3 FTP信息表 (ftp_info)

**表说明**：存储FTP连接相关信息，支持本地文件和SFTP两种方式，支持密码和密钥认证。

| 字段名                   | 数据类型    | 长度  | 是否为空 | 默认值 | 主键 | 索引 | 描述                                            |
|-----------------------|---------|-----|------|-----|----|----|-----------------------------------------------|
| id                    | int     | 11  | NO   | -   | PK | -  | 自增序号，主键                                       |
| file_type             | int     | 11  | YES  | 0   | -  | IDX| 文件落地方式：0=本地存储，1=FTP存储                        |
| ftp_name              | varchar | 100 | YES  | -   | -  | -  | FTP名称，便于识别                                   |
| description           | varchar | 200 | YES  | -   | -  | -  | 描述信息                                          |
| ftp_credential        | varchar | 10  | YES  | 'P' | -  | -  | FTP认证类型：P=密码，K=密钥                            |
| ftp_host              | varchar | 50  | YES  | -   | -  | -  | FTP服务器地址                                      |
| ftp_port              | int     | 11  | YES  | 22  | -  | -  | FTP端口号                                        |
| ftp_username          | varchar | 50  | YES  | -   | -  | -  | FTP用户名                                        |
| ftp_password          | varchar | 50  | YES  | -   | -  | -  | FTP密码（加密存储）                                  |
| private_key           | varchar | 200 | YES  | -   | -  | -  | 私钥文件路径                                        |
| status                | int     | 11  | YES  | 1   | -  | -  | 状态：0=停用，1=启用                                 |
| file_path             | varchar | 200 | YES  | -   | -  | -  | 文件路径                                          |
| file_name             | varchar | 100 | YES  | -   | -  | -  | 文件名模式，支持通配符*                                |
| has_file_header       | int     | 11  | YES  | 1   | -  | -  | 是否有文件头：0=无文件头，1=有文件头                        |
| file_content_format   | varchar | 100 | YES  | -   | -  | -  | 文件内容格式，字段位置映射，如："0,1,2,3,4,5,6,7,8,9"      |
| create_time           | datetime| -   | YES  | NOW()| -  | -  | 创建时间（默认当前时间）                                |
| update_time           | datetime| -   | YES  | NOW()| -  | -  | 更新时间，配置最后一次修改的时间                            |

**表属性**：
- 存储引擎：StarRocks OLAP
- 分桶策略：按ID哈希分桶，1个桶
- 副本数：3

**业务规则**：
- 密码字段需要加密存储
- 私钥文件需要安全存储
- 文件名支持通配符，如 `asset_*.csv`
- 文件内容格式用于解析CSV文件时的字段映射

#### 3.2.4 保单监控表 (policy_monitor)

**表说明**：存储保单监控基本信息，用于快速查询保单状态。

| 字段名           | 数据类型    | 长度 | 是否为空 | 默认值 | 主键 | 索引 | 描述                                    |
|---------------|---------|----|----|-----|----|----|---------------------------------------|
| id            | int     | 11 | NO | -   | PK | -  | 自增序号，主键                               |
| monitor_date  | date    | -  | YES| -   | -  | IDX| 监控日期                                  |
| policy_no     | varchar | 20 | YES| -   | -  | IDX| 保单号                                   |
| policy_status | varchar | 10 | YES| -   | -  | IDX| 保单状态                                  |

**表属性**：
- 存储引擎：StarRocks OLAP
- 分桶策略：按ID哈希分桶，1个桶
- 副本数：3

**业务规则**：
- 只保留当天数据，历史数据定期清理
- 用于快速统计和查询保单基本状态

#### 3.2.5 保单监控明细表 (policy_monitor_detail)

**表说明**：存储保单监控的详细信息，包含计价、风保、结息、资产推送等状态，是系统的核心业务表。

| 字段名                                        | 数据类型      | 长度   | 是否为空 | 默认值   | 主键 | 索引 | 描述                                      |
|--------------------------------------------|-----------|------|------|-------|----|----|-------------------------------------------|
| id                                         | int       | 11   | NO   | -     | PK | -  | 自增序号，主键                                 |
| monitor_date                               | date      | -    | YES  | -     | -  | IDX| 监控日期，用于分区                               |
| policy_no                                  | varchar   | 20   | YES  | -     | -  | IDX| 保单号                                     |
| policy_system                              | varchar   | 20   | YES  | -     | -  | IDX| 保单归属系统（核心系统、交易中心、阿里云等）                  |
| sales_channel                              | varchar   | 50   | YES  | -     | -  | IDX| 销售渠道                                    |
| product_code                               | varchar   | 20   | YES  | -     | -  | IDX| 产品编码                                    |
| product_name                               | varchar   | 100  | YES  | -     | -  | IDX| 产品名称                                    |
| policy_status                              | int       | 11   | YES  | -     | -  | IDX| 保单状态：0=有效，1=暂停，2=失效                    |
| pricing_status                             | int       | 11   | YES  | 2     | -  | IDX| 计价状态：0=已计价，1=未计价，2=不涉及                 |
| new_policy_core_status                     | int       | 11   | YES  | 2     | -  | IDX| 新单进核心状态：0=已进核心，1=未进核心，2=不涉及            |
| policy_change_core_status                  | int       | 11   | YES  | 2     | -  | IDX| 保全进核心状态：0=已进核心，1=未进核心，2=不涉及            |
| policy_change_queue_status                 | int       | 11   | YES  | 2     | -  | IDX| 保全出队列状态：0=已出队列，1=未出队列，2=不涉及            |
| pricing_date                               | date      | -    | YES  | NULL  | -  | IDX| 计价日期，最后一次成功计价的日期                        |
| risk_insurance_status                      | int       | 11   | YES  | 2     | -  | IDX| 风险保费状态：0=已扣除，1=未扣除，2=不涉及               |
| risk_insurance_date                        | date      | -    | YES  | NULL  | -  | IDX| 风险保费扣除日期，最后一次成功扣除的日期                    |
| interest_settlement_status                 | int       | 11   | YES  | 2     | -  | IDX| 结息状态：0=已结息，1=未结息，2=不涉及                 |
| interest_settlement_date                   | date      | -    | YES  | NULL  | -  | IDX| 结息日期，最后一次成功结息的日期                        |
| is_push_website                            | int       | 11   | YES  | 0     | -  | -  | 是否推送官网：0=否，1=是                          |
| asset_push_website_status                  | int       | 11   | YES  | 2     | -  | IDX| 资产推送官网状态：0=已推送，1=未推送，2=不涉及             |
| asset_push_website_date                    | date      | -    | YES  | NULL  | -  | -  | 资产推送官网日期，最后一次成功推送资产的日期                  |
| website_account_value                      | decimal   | 18,2 | YES  | 0.00  | -  | -  | 资产推送官网账户价值（保留2位小数，单位：元）                |
| website_accumulated_income                 | decimal   | 18,2 | YES  | 0.00  | -  | -  | 资产推送官网累计收益（保留2位小数，单位：元）                |
| website_daily_income                       | decimal   | 18,2 | YES  | 0.00  | -  | -  | 资产推送官网每日收益（保留2位小数，单位：元）                |
| website_daily_interest                     | decimal   | 18,2 | YES  | 0.00  | -  | -  | 资产推送官网当日利息（保留2位小数，单位：元）                |
| website_accumulated_interest               | decimal   | 18,2 | YES  | 0.00  | -  | -  | 资产推送官网累计利息（保留2位小数，单位：元）                |
| website_bonus_income                       | decimal   | 18,2 | YES  | 0.00  | -  | -  | 资产推送官网加息收益（保留2位小数，单位：元）                |
| is_push_microsite                          | int       | 11   | YES  | 0     | -  | -  | 是否推送官微：0=否，1=是                          |
| asset_push_microsite_status                | int       | 11   | YES  | 2     | -  | IDX| 资产推送官微状态：0=已推送，1=未推送，2=不涉及             |
| asset_push_microsite_date                  | date      | -    | YES  | NULL  | -  | -  | 资产推送官微日期，最后一次成功推送资产的日期                  |
| microsite_account_value                    | decimal   | 18,2 | YES  | 0.00  | -  | -  | 资产推送官微账户价值（保留2位小数，单位：元）                |
| microsite_accumulated_income               | decimal   | 18,2 | YES  | 0.00  | -  | -  | 资产推送官微累计收益（保留2位小数，单位：元）                |
| microsite_daily_income                     | decimal   | 18,2 | YES  | 0.00  | -  | -  | 资产推送官微每日收益（保留2位小数，单位：元）                |
| microsite_daily_interest                   | decimal   | 18,2 | YES  | 0.00  | -  | -  | 资产推送官微当日利息（保留2位小数，单位：元）                |
| microsite_accumulated_interest             | decimal   | 18,2 | YES  | 0.00  | -  | -  | 资产推送官微累计利息（保留2位小数，单位：元）                |
| microsite_bonus_income                     | decimal   | 18,2 | YES  | 0.00  | -  | -  | 资产推送官微加息收益（保留2位小数，单位：元）                |
| is_push_trade                              | int       | 11   | YES  | 0     | -  | -  | 是否推送交易：0=否，1=是                          |
| asset_push_trade_status                    | int       | 11   | YES  | 2     | -  | IDX| 资产推送交易状态：0=已推送，1=未推送，2=不涉及             |
| asset_push_trade_date                      | date      | -    | YES  | NULL  | -  | -  | 资产推送交易日期，最后一次成功推送资产的日期                  |
| trade_account_value                        | decimal   | 18,2 | YES  | 0.00  | -  | -  | 资产推送交易账户价值（保留2位小数，单位：元）                |
| trade_accumulated_income                   | decimal   | 18,2 | YES  | 0.00  | -  | -  | 资产推送交易累计收益（保留2位小数，单位：元）                |
| trade_daily_income                         | decimal   | 18,2 | YES  | 0.00  | -  | -  | 资产推送交易每日收益（保留2位小数，单位：元）                |
| trade_daily_interest                       | decimal   | 18,2 | YES  | 0.00  | -  | -  | 资产推送交易当日利息（保留2位小数，单位：元）                |
| trade_accumulated_interest                 | decimal   | 18,2 | YES  | 0.00  | -  | -  | 资产推送交易累计利息（保留2位小数，单位：元）                |
| trade_bonus_income                         | decimal   | 18,2 | YES  | 0.00  | -  | -  | 资产推送交易加息收益（保留2位小数，单位：元）                |
| is_push_channel                            | int       | 11   | YES  | 0     | -  | -  | 是否推送渠道：0=否，1=是                          |
| asset_push_channel_method                  | int       | 11   | YES  | 0     | -  | -  | 资产推送渠道方式：0=文件，1=接口                      |
| asset_push_channel_status                  | int       | 11   | YES  | 2     | -  | IDX| 资产推送渠道状态：0=已推送，1=未推送，2=不涉及             |
| asset_push_channel_date                    | date      | -    | YES  | NULL  | -  | -  | 资产推送渠道日期，最后一次成功推送资产的日期                  |
| channel_account_value                      | decimal   | 18,2 | YES  | 0.00  | -  | -  | 资产推送渠道账户价值（保留2位小数，单位：元）                |
| channel_accumulated_income                 | decimal   | 18,2 | YES  | 0.00  | -  | -  | 资产推送渠道累计收益（保留2位小数，单位：元）                |
| channel_daily_income                       | decimal   | 18,2 | YES  | 0.00  | -  | -  | 资产推送渠道每日收益（保留2位小数，单位：元）                |
| channel_daily_interest                     | decimal   | 18,2 | YES  | 0.00  | -  | -  | 资产推送渠道当日利息（保留2位小数，单位：元）                |
| channel_accumulated_interest               | decimal   | 18,2 | YES  | 0.00  | -  | -  | 资产推送渠道累计利息（保留2位小数，单位：元）                |
| channel_bonus_income                       | decimal   | 18,2 | YES  | 0.00  | -  | -  | 资产推送渠道加息收益（保留2位小数，单位：元）                |
| create_time                                | datetime  | -    | YES  | NOW() | -  | -  | 创建时间（默认当前时间）                            |
| update_time                                | datetime  | -    | YES  | NOW() | -  | -  | 更新时间，配置最后一次修改的时间                        |

**表属性**：
- 存储引擎：StarRocks OLAP
- 分区策略：按监控日期范围分区 `PARTITION BY RANGE(monitor_date)`
- 分桶策略：按保单号哈希分桶，32个桶 `DISTRIBUTED BY HASH(policy_no) BUCKETS 32`
- 副本数：3
- 数据保留：建议保留1年历史数据

**业务规则**：
- 每日全量投连保单生成监控明细数据
- 状态字段统一使用数字编码：0=成功/已处理，1=失败/未处理，2=不涉及
- 金额字段精度为2位小数，支持负数
- 日期字段为NULL表示未处理或不涉及
- 推送相关字段按目标系统分组（官网、官微、交易、渠道）

#### 3.2.6 监控汇总结果表 (monitor_summary_result)

**表说明**：存储每日监控的汇总结果，用于生成报表和预警通知。

| 字段名                                      | 数据类型    | 长度 | 是否为空 | 默认值   | 主键 | 索引 | 描述                                      |
|------------------------------------------|---------|----|----|-------|----|----|-------------------------------------------|
| id                                       | int     | 11 | NO | -     | PK | -  | 自增序号，主键                                 |
| monitor_date                             | date    | -  | YES| -     | -  | IDX| 监控日期                                    |
| total_policies                           | int     | 11 | YES| 0     | -  | -  | 总保单数                                    |
| pricing_total                            | int     | 11 | YES| 0     | -  | -  | 计价总数（已计价+未计价）                          |
| pricing_success                          | int     | 11 | YES| 0     | -  | -  | 计价成功数（已计价）                              |
| pricing_failed                           | int     | 11 | YES| 0     | -  | -  | 计价失败数（未计价）                              |
| pricing_not_applicable                   | int     | 11 | YES| 0     | -  | -  | 计价不涉及数                                  |
| risk_insurance_total                     | int     | 11 | YES| 0     | -  | -  | 风险保费总数（已扣除+未扣除）                        |
| risk_insurance_success                   | int     | 11 | YES| 0     | -  | -  | 风险保费成功数（已扣除）                            |
| risk_insurance_failed                    | int     | 11 | YES| 0     | -  | -  | 风险保费失败数（未扣除）                            |
| risk_insurance_not_applicable            | int     | 11 | YES| 0     | -  | -  | 风险保费不涉及数                                |
| interest_total                           | int     | 11 | YES| 0     | -  | -  | 结息总数（已结息+未结息）                          |
| interest_success                         | int     | 11 | YES| 0     | -  | -  | 结息成功数（已结息）                              |
| interest_failed                          | int     | 11 | YES| 0     | -  | -  | 结息失败数（未结息）                              |
| interest_not_applicable                  | int     | 11 | YES| 0     | -  | -  | 结息不涉及数                                  |
| asset_push_website_total                 | int     | 11 | YES| 0     | -  | -  | 资产推送官网总数（已推送+未推送）                      |
| asset_push_website_success               | int     | 11 | YES| 0     | -  | -  | 资产推送官网成功数（已推送）                          |
| asset_push_website_failed                | int     | 11 | YES| 0     | -  | -  | 资产推送官网失败数（未推送）                          |
| asset_push_website_not_applicable        | int     | 11 | YES| 0     | -  | -  | 资产推送官网不涉及数                              |
| asset_push_microsite_total               | int     | 11 | YES| 0     | -  | -  | 资产推送官微总数（已推送+未推送）                      |
| asset_push_microsite_success             | int     | 11 | YES| 0     | -  | -  | 资产推送官微成功数（已推送）                          |
| asset_push_microsite_failed              | int     | 11 | YES| 0     | -  | -  | 资产推送官微失败数（未推送）                          |
| asset_push_microsite_not_applicable      | int     | 11 | YES| 0     | -  | -  | 资产推送官微不涉及数                              |
| asset_push_trade_total                   | int     | 11 | YES| 0     | -  | -  | 资产推送交易总数（已推送+未推送）                      |
| asset_push_trade_success                 | int     | 11 | YES| 0     | -  | -  | 资产推送交易成功数（已推送）                          |
| asset_push_trade_failed                  | int     | 11 | YES| 0     | -  | -  | 资产推送交易失败数（未推送）                          |
| asset_push_trade_not_applicable          | int     | 11 | YES| 0     | -  | -  | 资产推送交易不涉及数                              |
| asset_push_channel_total                 | int     | 11 | YES| 0     | -  | -  | 资产推送渠道总数（已推送+未推送）                      |
| asset_push_channel_success               | int     | 11 | YES| 0     | -  | -  | 资产推送渠道成功数（已推送）                          |
| asset_push_channel_failed                | int     | 11 | YES| 0     | -  | -  | 资产推送渠道失败数（未推送）                          |
| asset_push_channel_not_applicable        | int     | 11 | YES| 0     | -  | -  | 资产推送渠道不涉及数                              |
| create_time                              | datetime| -  | YES| NOW() | -  | -  | 创建时间                                    |
| update_time                              | datetime| -  | YES| NOW() | -  | -  | 更新时间                                    |

**表属性**：
- 存储引擎：StarRocks OLAP
- 分桶策略：按ID哈希分桶，1个桶
- 副本数：3
- 数据保留：建议保留1年历史数据

**业务规则**：
- 每日定时任务生成汇总数据
- 用于生成预警通知和报表展示
- 统计规则：total = success + failed，不包含not_applicable
- 预警判断：failed > 0 则预警状态为"失败"，否则为"成功"

#### 3.2.7 批处理任务配置表 (batch_task_config)

**表说明**：配置和管理系统中的批处理任务。

| 字段名             | 数据类型    | 长度    | 是否为空 | 默认值   | 主键 | 索引 | 描述                                      |
|-----------------|---------|-------|------|-------|----|----|-------------------------------------------|
| id              | int     | 11    | NO   | -     | PK | -  | 自增序号，主键                                 |
| task_code       | varchar | 100   | YES  | -     | -  | UNI| 任务编码，唯一标识                               |
| task_name       | varchar | 100   | YES  | -     | -  | -  | 任务名称                                    |
| task_class      | varchar | 200   | YES  | -     | -  | -  | 任务类名，完整的Java类路径                        |
| cron_expression | varchar | 50    | YES  | -     | -  | -  | Cron表达式，定义任务执行时间                        |
| task_params     | varchar | 65533 | YES  | '{}'  | -  | -  | 任务参数，JSON格式                            |
| task_status     | int     | 11    | YES  | 1     | -  | IDX| 任务状态：0=停用，1=启用                         |
| description     | varchar | 500   | YES  | -     | -  | -  | 任务描述                                    |
| create_time     | datetime| -     | YES  | NOW() | -  | -  | 创建时间（默认当前时间）                            |
| update_time     | datetime| -     | YES  | NOW() | -  | -  | 更新时间，配置最后一次修改的时间                        |

**表属性**：
- 存储引擎：StarRocks OLAP
- 分桶策略：按ID哈希分桶，1个桶
- 副本数：3

**预置任务配置**：
- `ASSET_FILE_PARSE`：资产文件解析任务，每日凌晨2点执行
- `POLICY_MONITOR_DETAIL`：保单监控明细任务，每日凌晨2点30分执行
- `MONITOR_SUMMARY`：监控汇总任务，每日凌晨3点执行

#### 3.2.8 批处理任务日志表 (batch_task_log)

**表说明**：记录批处理任务的执行日志。

| 字段名            | 数据类型    | 长度    | 是否为空 | 默认值   | 主键 | 索引 | 描述                                      |
|----------------|---------|-------|------|-------|----|----|-------------------------------------------|
| id             | bigint  | 20    | NO   | -     | PK | -  | 自增序号，主键                                 |
| task_code      | varchar | 100   | YES  | -     | -  | IDX| 任务编码                                    |
| task_name      | varchar | 100   | YES  | -     | -  | -  | 任务名称                                    |
| start_time     | datetime| -     | YES  | -     | -  | IDX| 开始时间                                    |
| end_time       | datetime| -     | YES  | -     | -  | -  | 结束时间                                    |
| execution_time | bigint  | 20    | YES  | 0     | -  | -  | 执行时长（毫秒）                                |
| task_status    | int     | 11    | YES  | 2     | -  | IDX| 执行状态：0=失败，1=成功，2=执行中                   |
| message        | varchar | 65533 | YES  | -     | -  | -  | 执行结果信息和错误信息                             |
| create_time    | datetime| -     | YES  | NOW() | -  | -  | 创建时间（默认当前时间）                            |
| update_time    | datetime| -     | YES  | NOW() | -  | -  | 更新时间                                    |

**表属性**：
- 存储引擎：StarRocks OLAP
- 分桶策略：按ID哈希分桶，1个桶
- 副本数：3
- 数据保留：建议保留3个月历史数据

**业务规则**：
- 每次任务执行都记录日志
- 执行时长 = 结束时间 - 开始时间
- 异常信息记录在message字段中
- 支持任务执行历史查询和统计分析

### 3.3 系统管理表结构详细设计（基于若依框架）

#### 3.3.1 用户信息表 (sys_user)

**表说明**：存储系统用户信息，支持RBAC权限控制。

| 字段名          | 数据类型    | 长度 | 是否为空 | 默认值 | 主键 | 索引 | 描述                                      |
|--------------|---------|----|----|-----|----|----|-------------------------------------------|
| user_id      | bigint  | 20 | NO | -   | PK | -  | 用户ID，主键                                 |
| dept_id      | bigint  | 20 | YES| -   | -  | IDX| 部门ID，关联sys_dept表                       |
| user_name    | varchar | 30 | NO | -   | -  | UNI| 用户账号，唯一                                |
| nick_name    | varchar | 30 | NO | -   | -  | -  | 用户昵称                                    |
| user_type    | varchar | 2  | YES| '00'| -  | -  | 用户类型（00=系统用户）                          |
| email        | varchar | 50 | YES| -   | -  | -  | 用户邮箱                                    |
| phone_number | varchar | 11 | YES| -   | -  | -  | 手机号码                                    |
| sex          | char    | 1  | YES| '0' | -  | -  | 用户性别（0=男，1=女，2=未知）                     |
| password     | varchar | 100| YES| -   | -  | -  | 密码（加密存储）                                |
| status       | char    | 1  | YES| '0' | -  | IDX| 帐号状态（0=正常，1=停用）                        |
| del_flag     | char    | 1  | YES| '0' | -  | -  | 删除标志（0=存在，2=删除）                        |
| create_time  | datetime| -  | YES| NOW()| -  | -  | 创建时间                                    |
| update_time  | datetime| -  | YES| NOW()| -  | -  | 更新时间                                    |

#### 3.3.2 角色信息表 (sys_role)

**表说明**：存储系统中的角色信息，支持数据权限控制。

| 字段名                 | 数据类型    | 长度 | 是否为空 | 默认值 | 主键 | 索引 | 描述                                      |
|---------------------|---------|----|----|-----|----|----|-------------------------------------------|
| role_id             | bigint  | 20 | NO | -   | PK | -  | 角色ID                                    |
| role_name           | varchar | 30 | NO | -   | -  | -  | 角色名称                                    |
| role_key            | varchar | 100| NO | -   | -  | UNI| 角色权限字符串，唯一                             |
| role_sort           | int     | 11 | NO | -   | -  | -  | 显示顺序                                    |
| data_scope          | char    | 1  | YES| '1' | -  | -  | 数据范围（1=全部，2=自定义，3=本部门，4=本部门及以下）        |
| menu_check_strictly | tinyint | 4  | YES| 1   | -  | -  | 菜单树选择项是否关联显示                            |
| dept_check_strictly | tinyint | 4  | YES| 1   | -  | -  | 部门树选择项是否关联显示                            |
| status              | char    | 1  | NO | '0' | -  | IDX| 角色状态（0=正常，1=停用）                        |
| del_flag            | char    | 1  | YES| '0' | -  | -  | 删除标志（0=存在，2=删除）                        |
| create_time         | datetime| -  | YES| NOW()| -  | -  | 创建时间                                    |
| update_time         | datetime| -  | YES| NOW()| -  | -  | 更新时间                                    |
| remark              | varchar | 500| YES| -   | -  | -  | 备注                                      |

#### 3.3.3 菜单权限表 (sys_menu)

**表说明**：存储系统菜单和权限信息，支持树形结构。

| 字段名         | 数据类型    | 长度 | 是否为空 | 默认值 | 主键 | 索引 | 描述                                      |
|-------------|---------|----|----|-----|----|----|-------------------------------------------|
| menu_id     | bigint  | 20 | NO | -   | PK | -  | 菜单ID                                    |
| menu_name   | varchar | 50 | NO | -   | -  | -  | 菜单名称                                    |
| parent_id   | bigint  | 20 | YES| 0   | -  | IDX| 父菜单ID                                   |
| order_num   | int     | 11 | YES| 0   | -  | -  | 显示顺序                                    |
| path        | varchar | 200| YES| -   | -  | -  | 路由地址                                    |
| component   | varchar | 255| YES| -   | -  | -  | 组件路径                                    |
| query       | varchar | 255| YES| -   | -  | -  | 路由参数                                    |
| is_frame    | int     | 11 | YES| 1   | -  | -  | 是否为外链（0=是，1=否）                         |
| is_cache    | int     | 11 | YES| 0   | -  | -  | 是否缓存（0=缓存，1=不缓存）                       |
| menu_type   | char    | 1  | YES| 'C' | -  | IDX| 菜单类型（M=目录，C=菜单，F=按钮）                   |
| visible     | char    | 1  | YES| '0' | -  | -  | 菜单状态（0=显示，1=隐藏）                        |
| status      | char    | 1  | YES| '0' | -  | IDX| 菜单状态（0=正常，1=停用）                        |
| perms       | varchar | 100| YES| -   | -  | -  | 权限标识                                    |
| icon        | varchar | 100| YES| -   | -  | -  | 菜单图标                                    |
| create_time | datetime| -  | YES| NOW()| -  | -  | 创建时间                                    |
| update_time | datetime| -  | YES| NOW()| -  | -  | 更新时间                                    |
| remark      | varchar | 500| YES| -   | -  | -  | 备注                                      |

#### 3.3.4 部门表 (sys_dept)

**表说明**：存储系统组织架构中的部门信息，支持树形结构。

| 字段名         | 数据类型    | 长度 | 是否为空 | 默认值 | 主键 | 索引 | 描述                                      |
|-------------|---------|----|----|-----|----|----|-------------------------------------------|
| dept_id     | bigint  | 20 | NO | -   | PK | -  | 部门ID                                    |
| parent_id   | bigint  | 20 | YES| 0   | -  | IDX| 父部门ID                                   |
| ancestors   | varchar | 50 | YES| -   | -  | -  | 祖级列表                                    |
| dept_name   | varchar | 30 | YES| -   | -  | -  | 部门名称                                    |
| order_num   | int     | 11 | YES| 0   | -  | -  | 显示顺序                                    |
| leader      | varchar | 20 | YES| -   | -  | -  | 负责人                                     |
| phone       | varchar | 11 | YES| -   | -  | -  | 联系电话                                    |
| email       | varchar | 50 | YES| -   | -  | -  | 邮箱                                      |
| status      | char    | 1  | YES| '0' | -  | IDX| 部门状态（0=正常，1=停用）                        |
| del_flag    | char    | 1  | YES| '0' | -  | -  | 删除标志（0=存在，2=删除）                        |
| create_time | datetime| -  | YES| NOW()| -  | -  | 创建时间                                    |
| update_time | datetime| -  | YES| NOW()| -  | -  | 更新时间                                    |

#### 3.3.5 关联表设计

**用户角色关联表 (sys_user_role)**：
- user_id (bigint): 用户ID
- role_id (bigint): 角色ID
- 联合主键：(user_id, role_id)

**角色菜单关联表 (sys_role_menu)**：
- role_id (bigint): 角色ID
- menu_id (bigint): 菜单ID
- 联合主键：(role_id, menu_id)

**角色部门关联表 (sys_role_dept)**：
- role_id (bigint): 角色ID
- dept_id (bigint): 部门ID
- 联合主键：(role_id, dept_id)

#### 3.3.6 系统日志表设计

**登录日志表 (sys_logininfor)**：

| 字段名            | 数据类型    | 长度 | 是否为空 | 默认值 | 主键 | 索引 | 描述                                      |
|----------------|---------|----|----|-----|----|----|-------------------------------------------|
| info_id        | bigint  | 20 | NO | -   | PK | -  | 日志ID                                    |
| user_name      | varchar | 50 | YES| -   | -  | IDX| 用户账号                                    |
| ipaddr         | varchar | 128| YES| -   | -  | -  | 登录IP地址                                  |
| login_location | varchar | 255| YES| -   | -  | -  | 登录地点                                    |
| browser        | varchar | 50 | YES| -   | -  | -  | 浏览器类型                                   |
| os             | varchar | 50 | YES| -   | -  | -  | 操作系统                                    |
| status         | char    | 1  | YES| '0' | -  | IDX| 登录状态（0=成功，1=失败）                        |
| msg            | varchar | 255| YES| -   | -  | -  | 提示消息                                    |
| login_time     | datetime| -  | YES| NOW()| -  | IDX| 访问时间                                    |

**操作日志表 (sys_oper_log)**：

| 字段名            | 数据类型    | 长度  | 是否为空 | 默认值 | 主键 | 索引 | 描述                                      |
|----------------|---------|-----|----|-----|----|----|-------------------------------------------|
| oper_id        | bigint  | 20  | NO | -   | PK | -  | 日志ID                                    |
| title          | varchar | 50  | YES| -   | -  | IDX| 模块标题                                    |
| business_type  | int     | 11  | YES| 0   | -  | IDX| 业务类型（0=其它，1=新增，2=修改，3=删除）              |
| method         | varchar | 100 | YES| -   | -  | -  | 方法名称                                    |
| request_method | varchar | 10  | YES| -   | -  | -  | 请求方式                                    |
| operator_type  | int     | 11  | YES| 0   | -  | -  | 操作类别（0=其它，1=后台用户，2=手机端用户）              |
| oper_name      | varchar | 50  | YES| -   | -  | IDX| 操作人员                                    |
| dept_name      | varchar | 50  | YES| -   | -  | -  | 部门名称                                    |
| oper_url       | varchar | 255 | YES| -   | -  | -  | 请求URL                                   |
| oper_ip        | varchar | 128 | YES| -   | -  | -  | 主机地址                                    |
| oper_location  | varchar | 255 | YES| -   | -  | -  | 操作地点                                    |
| oper_param     | varchar | 2000| YES| -   | -  | -  | 请求参数                                    |
| json_result    | varchar | 2000| YES| -   | -  | -  | 返回参数                                    |
| status         | int     | 11  | YES| 0   | -  | IDX| 操作状态（0=正常，1=异常）                        |
| error_msg      | varchar | 2000| YES| -   | -  | -  | 错误消息                                    |
| oper_time      | datetime| -   | YES| NOW()| -  | IDX| 操作时间                                    |

### 3.4 数据字典详细定义

#### 3.4.1 状态码字典

**保单状态 (policy_status)**：
- `0`：有效 - 保单正常有效状态
- `1`：暂停 - 保单暂时停止，可恢复
- `2`：失效 - 保单永久失效，不可恢复

**计价状态 (pricing_status)**：
- `0`：已计价 - 应计价条数=实际计价条数
- `1`：未计价 - 应计价条数≠实际计价条数
- `2`：不涉及 - 无应计价数据

**风险保费状态 (risk_insurance_status)**：
- `0`：已扣除 - 应扣除条数=实际扣除条数
- `1`：未扣除 - 应扣除条数≠实际扣除条数
- `2`：不涉及 - 不需要扣除风险保费

**结息状态 (interest_settlement_status)**：
- `0`：已结息 - 应结息条数=实际结息条数
- `1`：未结息 - 应结息条数≠实际结息条数
- `2`：不涉及 - 没有需要结息的数据

**资产推送状态 (asset_push_*_status)**：
- `0`：已推送 - 资产已成功推送到目标系统
- `1`：未推送 - 资产未推送或推送失败
- `2`：不涉及 - 无需推送资产

**新单进核心状态 (new_policy_core_status)**：
- `0`：已进核心 - 新单已成功进入核心系统
- `1`：未进核心 - 新单未进入核心系统
- `2`：不涉及 - 不需要进核心系统

**保全进核心状态 (policy_change_core_status)**：
- `0`：已进核心 - 保全已成功进入核心系统
- `1`：未进核心 - 保全未进入核心系统
- `2`：不涉及 - 不需要进核心系统

**保全出队列状态 (policy_change_queue_status)**：
- `0`：已出队列 - 保全已成功出队列
- `1`：未出队列 - 保全未出队列
- `2`：不涉及 - 不需要出队列

#### 3.4.2 配置类型字典

**FTP认证类型 (ftp_credential)**：
- `P`：密码认证 - 使用用户名密码方式认证
- `K`：密钥认证 - 使用私钥文件方式认证

**文件落地方式 (file_type)**：
- `0`：本地存储 - 文件存储在本地路径
- `1`：FTP存储 - 文件存储在FTP服务器

**资产推送渠道方式 (asset_push_channel_method)**：
- `0`：文件方式 - 通过文件推送资产数据
- `1`：接口方式 - 通过API接口推送资产数据

**任务状态 (task_status)**：
- `0`：停用 - 任务已停用，不会执行
- `1`：启用 - 任务已启用，按计划执行

**执行状态 (execution_status)**：
- `0`：失败 - 任务执行失败
- `1`：成功 - 任务执行成功
- `2`：执行中 - 任务正在执行

#### 3.4.3 系统参数字典

**用户状态 (user_status)**：
- `0`：正常 - 用户账号正常可用
- `1`：停用 - 用户账号已停用

**角色状态 (role_status)**：
- `0`：正常 - 角色正常可用
- `1`：停用 - 角色已停用

**菜单类型 (menu_type)**：
- `M`：目录 - 菜单目录，包含子菜单
- `C`：菜单 - 具体的菜单页面
- `F`：按钮 - 页面内的操作按钮

**数据范围 (data_scope)**：
- `1`：全部数据权限 - 可查看所有数据
- `2`：自定数据权限 - 按自定义规则查看数据
- `3`：本部门数据权限 - 只能查看本部门数据
- `4`：本部门及以下数据权限 - 可查看本部门及下级部门数据

### 3.5 业务字段详细说明

#### 3.5.1 保单基础信息字段

**保单号 (policy_no)**：
- 数据来源：以核心系统为基础
- 关联规则：关联交易中心为进核心数据，关联阿里云为未进核心数据
- 业务规则：保单来源应为阿里云（阿里云暂无新单）
- 格式要求：最大长度20位，支持字母数字组合

**保单归属系统 (policy_system)**：
- 数据来源：各业务系统打系统标签
- 可选值：核心系统、交易中心、阿里云等
- 默认规则：未打标签的默认为核心系统
- 业务意义：用于确定数据取值来源

**销售渠道 (sales_channel)**：
- 数据来源：以各业务系统记录为准
- 业务意义：标识保单的销售来源渠道
- 用途：用于渠道维度的统计分析

**产品编码 (product_code)**：
- 数据来源：以各业务系统记录为准
- 业务意义：产品的内部编码标识
- 关联关系：与产品名称一一对应

**产品名称 (product_name)**：
- 数据来源：以各业务系统记录为准
- 显示规则：显示对外包装的名称，如小金罐、小金余
- 业务意义：用户可见的产品名称

#### 3.5.2 状态监控字段

**计价相关字段**：
- 数据来源：阿里云保单从阿里云系统取值，其他从核心系统取值
- 状态判断：应计价条数=实际计价条数为"已计价"，否则为"未计价"
- 应计价条数：与保单来源系统计价抽数逻辑保持一致
- 实际计价条数：计价日期为当天的数据
- 计价日期：系统记录的真实计价日期，未计价时为NULL

**风险保费相关字段**：
- 数据来源：阿里云保单从阿里云系统取值，其他从核心系统取值
- 状态判断：应扣除条数=实际扣除条数为"已扣除"，否则为"未扣除"
- 应扣除条数：与保单来源系统风险保费抽数逻辑保持一致
- 实际扣除条数：风险保费扣除日期为当天的数据
- 扣除日期：系统记录的真实扣除日期，未扣除时为NULL

**结息相关字段**：
- 数据来源：阿里云保单从阿里云系统取值，其他从核心系统取值
- 状态判断：应结息条数=实际结息条数为"已结息"，否则为"未结息"
- 结息日期：系统记录的结息日期，未结息时为NULL

#### 3.5.3 资产推送字段

**推送目标系统**：
- 官网：推送到官方网站系统
- 官微：推送到官方微信系统
- 交易：推送到交易中心系统
- 渠道：推送到各销售渠道系统

**推送状态判断**：
- 已推送：目标系统中存在对应数据且数据完整
- 未推送：目标系统中不存在数据或数据不完整
- 不涉及：该保单无需推送到该目标系统

**推送金额字段**：
- 账户价值：保单当前的账户总价值
- 累计收益：保单从开始到现在的总收益
- 每日收益：保单当日产生的收益
- 当日利息：保单当日产生的利息
- 累计利息：保单从开始到现在的总利息
- 加息收益：保单享受的额外加息收益

**数据精度要求**：
- 所有金额字段保留2位小数
- 单位统一为人民币元
- 支持负数（如亏损情况）
- 默认值为0.00

### 3.6 索引设计和性能优化

#### 3.6.1 主要索引设计

**资产文件明细表 (asset_file_detail)**：
```sql
-- 主键索引
PRIMARY KEY (id)
-- 分区字段索引
INDEX idx_monitor_date (monitor_date)
-- 业务查询索引
INDEX idx_policy_no (policy_no)
INDEX idx_file_name (asset_file_name)
INDEX idx_file_create_date (file_create_date)
-- 复合索引
INDEX idx_date_policy (monitor_date, policy_no)
```

**保单监控明细表 (policy_monitor_detail)**：
```sql
-- 主键索引
PRIMARY KEY (id)
-- 分区字段索引
INDEX idx_monitor_date (monitor_date)
-- 核心业务索引
INDEX idx_policy_no (policy_no)
INDEX idx_policy_system (policy_system)
INDEX idx_sales_channel (sales_channel)
INDEX idx_product_code (product_code)
INDEX idx_product_name (product_name)
-- 状态查询索引
INDEX idx_policy_status (policy_status)
INDEX idx_pricing_status (pricing_status)
INDEX idx_risk_insurance_status (risk_insurance_status)
INDEX idx_interest_status (interest_settlement_status)
INDEX idx_push_website_status (asset_push_website_status)
INDEX idx_push_microsite_status (asset_push_microsite_status)
INDEX idx_push_trade_status (asset_push_trade_status)
INDEX idx_push_channel_status (asset_push_channel_status)
-- 日期查询索引
INDEX idx_pricing_date (pricing_date)
INDEX idx_risk_insurance_date (risk_insurance_date)
INDEX idx_interest_date (interest_settlement_date)
-- 复合索引
INDEX idx_date_policy_system (monitor_date, policy_no, policy_system)
INDEX idx_date_status (monitor_date, policy_status)
```

**监控汇总结果表 (monitor_summary_result)**：
```sql
-- 主键索引
PRIMARY KEY (id)
-- 业务查询索引
INDEX idx_monitor_date (monitor_date)
-- 唯一索引
UNIQUE INDEX uk_monitor_date (monitor_date)
```

**配置信息表 (config_info)**：
```sql
-- 主键索引
PRIMARY KEY (id)
-- 业务查询索引
INDEX idx_config_type (config_type)
INDEX idx_config_def (config_def)
-- 复合索引
INDEX idx_type_def (config_type, config_def)
```

#### 3.6.2 分区策略详细设计

**按日期范围分区 (资产文件明细表、保单监控明细表)**：
```sql
PARTITION BY RANGE(monitor_date) (
    PARTITION p20250101 VALUES [('2025-01-01'), ('2025-02-01')),
    PARTITION p20250201 VALUES [('2025-02-01'), ('2025-03-01')),
    PARTITION p20250301 VALUES [('2025-03-01'), ('2025-04-01')),
    -- 按月分区，便于数据管理和查询优化
    ...
)
```

**分桶策略说明**：
- 资产文件明细表：按policy_no哈希分桶，16个桶
- 保单监控明细表：按policy_no哈希分桶，32个桶
- 其他表：按主键哈希分桶，1个桶

#### 3.6.3 查询优化建议

**常用查询模式优化**：
1. **按日期查询**：利用分区裁剪，只查询相关分区
2. **按保单号查询**：利用分桶策略，快速定位数据
3. **状态统计查询**：利用状态字段索引，提高聚合效率
4. **多维度查询**：利用复合索引，减少回表操作

**SQL优化建议**：
```sql
-- 推荐：利用分区裁剪
SELECT * FROM policy_monitor_detail
WHERE monitor_date = '2025-08-19'
  AND policy_no = 'P123456789';

-- 推荐：利用索引覆盖
SELECT monitor_date, policy_no, policy_status
FROM policy_monitor_detail
WHERE monitor_date BETWEEN '2025-08-01' AND '2025-08-31'
  AND policy_status = 0;

-- 避免：全表扫描
SELECT * FROM policy_monitor_detail
WHERE product_name LIKE '%金罐%';
```

#### 3.6.4 数据生命周期管理

**数据保留策略**：
- 资产文件明细表：保留1年历史数据
- 保单监控明细表：保留1年历史数据
- 监控汇总结果表：保留1年历史数据
- 批处理任务日志表：保留3个月历史数据
- 系统日志表：保留6个月历史数据

**数据清理策略**：
```sql
-- 定期清理历史数据（建议每月执行）
-- 删除1年前的分区数据
ALTER TABLE policy_monitor_detail DROP PARTITION p20240101;

-- 清理日志数据
DELETE FROM batch_task_log
WHERE create_time < DATE_SUB(NOW(), INTERVAL 3 MONTH);
```

#### 3.6.5 性能监控指标

**关键性能指标**：
- 查询响应时间：平均响应时间 < 3秒
- 并发处理能力：支持1000+并发查询
- 数据插入性能：批量插入 > 10000条/秒
- 存储空间使用：压缩比 > 3:1

**监控SQL示例**：
```sql
-- 查询性能监控
SELECT
    table_name,
    avg_query_time,
    max_query_time,
    query_count
FROM information_schema.table_statistics
WHERE table_schema = 'huida_monitor';

-- 存储空间监控
SELECT
    table_name,
    data_length,
    index_length,
    (data_length + index_length) as total_size
FROM information_schema.tables
WHERE table_schema = 'huida_monitor';
```

## 4. 功能详细设计

### 4.1 保单监控业务逻辑

#### 4.1.1 数据来源规则
- **保单基础信息**：以核心系统为基础
- **计价数据**：阿里云保单从阿里云系统取值，其他从核心系统取值
- **风险保费数据**：按保单来源系统分别取值
- **结息数据**：按保单来源系统分别取值
- **资产推送数据**：分别从官网、官微、交易中心、渠道文件获取

#### 4.1.2 状态判断逻辑
- **计价状态**：应计价条数=实际计价条数为"已计价"，否则为"未计价"
- **风险保费状态**：应扣除条数=实际扣除条数为"已扣除"，否则为"未扣除"
- **结息状态**：应结息条数=实际结息条数为"已结息"，否则为"未结息"
- **资产推送状态**：根据各目标系统数据判断推送状态

### 4.2 批处理任务设计

#### 4.2.1 资产文件解析批处理
- **触发方式**：定时触发
- **处理逻辑**：根据FTP配置下载文件到本地，解析并保存到资产文件明细表
- **异常处理**：支持解析失败重试机制

#### 4.2.2 保单监控明细批处理
- **触发方式**：定时触发
- **处理逻辑**：根据资产文件明细表更新保单监控明细表
- **汇总统计**：生成监控汇总结果并发送企微通知

### 4.3 预警设计

#### 4.3.1 预警内容格式
```
【2025-08-19日，投连保单监控结果如下】
【计价监控】【成功/失败】【应计价保单XX条，未计价YY条】
【风险保费监控】【成功/失败】【应扣除风险保费保单XX条，未扣YY条】
【结息监控】【成功/失败】【应结息保单XX条，未结息YY条】
【资产推送官网监控】【成功/失败】【资产应推送官网保单XX条，未推送YY条】
【资产推送官微监控】【成功/失败】【资产应推送官微保单XX条，未推送YY条】
【资产推送交易监控】【成功/失败】【资产应推送交易保单XX条，未推送YY条】
【资产推送渠道监控】【成功/失败】【资产应推送渠道保单XX条，未推送YY条】
```

#### 4.3.2 预警规则
- 按保单去重进行汇总
- XX = 状态"已处理" + 状态"未处理"
- YY = 状态"未处理"，若YY=0则预警状态为"成功"，否则为"失败"

## 5. 系统非功能需求

### 5.1 性能需求
- 支持高并发查询（1000+并发用户）
- 大数据量下保持查询性能（千万级数据）
- 页面响应时间不超过3秒
- 批处理任务在规定时间内完成

### 5.2 安全需求
- 基于RBAC的权限控制
- 用户登录认证（Spring Security）
- 操作日志记录
- 数据传输加密
- 敏感数据脱敏

### 5.3 可用性需求
- 系统可用性不低于99.9%
- 支持故障自动恢复
- 提供友好的用户界面
- 支持移动端访问

### 5.4 可维护性需求
- 模块化设计，便于维护和扩展
- 提供完善的日志记录
- 支持在线升级和配置修改
- 代码规范，注释完整

## 6. 部署架构设计

### 6.1 数据同步架构
- 从交易中心库、核心库、阿里云库同步数据到大数据平台
- 在大数据平台进行数据加工处理
- 加工完成后同步到StarRocks库
- 支持增量自动同步

### 6.2 应用部署架构
- 前后端分离部署
- 后端服务集群部署
- 前端静态资源CDN部署
- 数据库读写分离

## 7. 风险评估与应对

### 7.1 技术风险
- **数据同步失败**：增加监控和自动重试机制
- **性能瓶颈**：优化查询和申请独享资源
- **系统故障**：建立容灾备份机制

### 7.2 业务风险
- **数据准确性**：建立数据校验机制
- **监控遗漏**：完善监控覆盖范围
- **预警延迟**：优化处理流程

## 8. 验收标准

### 8.1 功能验收
- 所有功能模块正常运行
- 数据同步功能稳定
- 监控数据准确完整
- 预警功能及时有效

### 8.2 性能验收
- 查询响应时间符合要求
- 并发处理能力达标
- 系统稳定性满足要求

### 8.3 安全验收
- 权限控制有效
- 数据安全保障
- 日志记录完整

## 9. 项目交付物

### 9.1 技术文档
- 系统设计文档
- 数据库设计文档
- API接口文档
- 部署运维文档

### 9.2 代码交付
- 后端源代码
- 前端源代码
- 数据库脚本
- 配置文件

### 9.3 测试文档
- 测试用例
- 测试报告
- 性能测试报告
