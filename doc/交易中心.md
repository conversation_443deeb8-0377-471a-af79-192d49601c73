# 交易中心

## 一、交易中心投连单及保单状态

```sql
SELECT
a.`policy_no` 保单号,
a.`channel_code` 渠道编码,
b.`source_name` 渠道名称,
case
WHEN a.`policy_status` = '0' then '有效'
WHEN a.`policy_status` = '1' then '无效'
WHEN a.`policy_status` = '2' then '全额领取'
WHEN a.`policy_status` = '4' then 'T0转换未复活'
end '保单状态'
FROM
`prd_ac_policy` a
JOIN `t_channel_source_set` b on a.`channel_code` = b.`source_code`;
```

## 二、京东资产同步

### （一）京东小金罐

1. **资产文件路径：**
   ```
   /yunshare/edoc/Web/NewPolicyAmnt/143/YY/mm/dd
   ```

2. **交易中心kettle读取：**
   ```bash
   #小金罐-核心收益同步(19:30:00)
   30 19 * * * cd /opt/kettle/pdi-ce-*******-371/data-integration/ && ./kitchen.sh -file=/opt/kettle/pdi-ce-*******-371/kettle/com/huida/account/core/assets/assets.kjb -param active=prod >> /alilog/log/kettle/newassets/newassets_$(date +"%Y-%m-%d_%H-%M-%S").log
   ```

3. **同步结果核实：**
   ```sql
   SELECT a.statisCount,a.syncEarnState  ,
   case when syncEarnState=0 then '未处理条数'
   when syncEarnState=1 then '处理成功条数'
   when syncEarnState=2 then '锁定中保单资产条数'
   when syncEarnState=3 then '多推送条数'
   when syncEarnState=4 then '其他产品条数'
   when syncEarnState=5 then '资产数量与账户不匹配条数'
   when syncEarnState=6 then '临时表数据异常条数'
   when syncEarnState=7 then '上个交易日未同步收益条数'
   when syncEarnState=8 then '其他异常条数' end '类型'
   FROM
   (select count(t.son_account_his_id) as statisCount,t.sync_earn_state as syncEarnState
   FROM `tmp_son_account_his` t join `prd_ac_policy` b ON t.`policy_no` = b.`policy_no` and t.`core_prd_account` = b.`core_prd_account` WHERE b.`channel_code` = 'RPM0011201' and b.`product_code` in (SELECT `product_code` FROM `prd_account_config` WHERE `site_id` = 'site20222918000001' and `account_group` = 'XJG_ONE') and b.`policy_status` = '0' group by t.sync_earn_state having count(t.son_account_his_id) >0)  as a ;
   ```

4. **同步渠道方式：**
   ```
   Mq消费同步渠道，例：topic:prod_callback_insurance,tag:trade_callback_new,messageid:0AC110FC000105474C6C234170435A47,body:{"notifyType":"policyEarnCallBack","earnTime":"2025-07-04","policyNo":"86000020221600005157"}
   ```

5. **同步渠道结果核实：**
   ```sql
   SELECT `is_send` ,count(1) ,
   case when `is_send` = '0' then '未同步'
   when `is_send`  = '1' then '同步成功' end '同步结果'
   FROM `son_account_his` where `policy_no` in (SELECT `policy_no` FROM `prd_ac_policy` WHERE `channel_code` = 'RPM0011201' and `product_code` in (SELECT `product_code` FROM `prd_account_config` WHERE `site_id` = 'site20222918000001' and `account_group` = 'XJG_ONE') and `policy_status` = '0') and `earn_time`  = '2025-07-07' GROUP BY `is_send` ;
   ```

### （二）京东T0

1. **资产文件路径：**
   ```
   /yunshare/edoc/Web/NewPolicyAmnt/T0/YY/mm/dd
   ```

2. **交易中心kettle读取：**
   ```bash
   #T0收益同步(20:20)
   20 20 * * *  cd /opt/kettle/pdi-ce-*******-371/data-integration/ && ./kitchen.sh -file=/opt/kettle/pdi-ce-*******-371/kettle/com/huida/synch/t0assets/assets.kjb -param active=prod >> /alilog/log/kettle/newassets/t0newassets_$(date +"%Y-%m-%d_%H-%M-%S").log
   ```

3. **同步结果核实：**
   ```sql
   SELECT a.statisCount,a.syncEarnState  ,
   case when a.syncEarnState = 0 then '未处理条数'
   when a.syncEarnState = 1 then '处理成功条数'
   when a.syncEarnState = 2 then '锁定中保单资产条数'
   when a.syncEarnState = 3 then '多推送条数'
   when a.syncEarnState = 4 then '其他产品条数'
   when a.syncEarnState = 5 then '资产数量与账户不匹配条数'
   when a.syncEarnState = 6 then '临时表数据异常条数'
   when a.syncEarnState = 7 then '上个交易日未同步收益条数'
   when a.syncEarnState = 9 then '超阈值校验'   end '类型'
   FROM
   (select count(t.son_account_his_id) as statisCount,t.sync_earn_state as syncEarnState
   FROM `t0_tmp_son_account_his` t join `prd_ac_policy` b ON t.`policy_no` = b.`policy_no` and t.`core_prd_account` = b.`core_prd_account` WHERE b.`channel_code` = 'RPM0011201' and b.`product_code` in (SELECT `product_code` FROM `prd_account_config` WHERE `site_id` = 'site20222918000001' and `account_group` = 'XJB_T0') and b.`policy_status` = '0' group by t.sync_earn_state having count(t.son_account_his_id) >0)  as a;
   ```

4. **同步渠道方式：**
   ```
   Mq消费同步渠道，例：
   topic:prod_callback_insurance,tag:trade_callback_new,messageid:0AC11021000105474C6C286F206A1B89,body:{"notifyType":"policyEarnCallBack","earnTime":"2025-07-07","policyNo":"86000020160259163395"}
   ```

5. **同步渠道结果核实：**
   ```sql
   SELECT `is_send` ,count(1) ,
   case when `is_send` = '0' then '未同步'
   when `is_send`  = '1' then '同步成功' end '同步结果'
   FROM `son_account_his` where `policy_no` in (SELECT `policy_no` FROM `prd_ac_policy` WHERE `channel_code` = 'RPM0011201' and `product_code` in (SELECT `product_code` FROM `prd_account_config` WHERE `site_id` = 'site20222918000001' and `account_group` = 'XJB_T0') and `policy_status` = '0') and `earn_time`  = '2025-07-07' GROUP BY `is_send` ;
   ```

### （三）京东团单

1. **资产文件路径：**
   ```
   /yunshare/edoc/Web/JDGrpNewAccSync/02/YY/mm/dd/TL   京东团单资产文件
   /yunshare/edoc/Web/JDGrpNewAccSync/GrpNewAccSyncEndFile/2025-02-26.xml   京东团单资产文件批处理结束文件
   /yunshare/edoc/Web/JDGrpNewPolicyDetail/02/YY/mm/dd/26/TLDetail    京东团单明细文件
   /yunshare/edoc/Web/JDGrpNewPolicyDetail/GrpNewPolicyDetailEndFile/2025-02-26.xml    京东团单明细文件批处理结束文件
   ```

2. **交易中心kettle读取：**
   ```bash
   #JD团险明细同步、资产同步(20:30:00)
   30 20 * * *  cd /opt/kettle/pdi-ce-*******-371/data-integration/ && ./kitchen.sh -file=/opt/kettle/pdi-ce-*******-371/kettle/com/huida/account/core/ac_grp_jd/newPolicyDetail.kjb -param active=prod  >> /alilog/log/kettle/ac_gro_jdPolicyDetail/newPolicyDetail_$(date +"%Y-%m-%d_%H-%M-%S").log
   ```

3. **同步结果核实：**
   ```sql
   SELECT count(1) FROM `ac_grp_policy` WHERE `channel_code` = 'RPM0011201' and `policy_status` = '0';
   SELECT `sync_earn_state`,count(1),
   case when `sync_earn_state` = '1' then '同步成功'
   when `sync_earn_state` = '0' then '未同步' end '同步状态'
   FROM `ac_grp_son_account_his` WHERE `policy_no` in (SELECT policy_no FROM `ac_grp_policy` WHERE `channel_code` = 'RPM0011201' and `policy_status` = '0') and `earn_time` = '2025-07-08' GROUP BY `sync_earn_state` ;
   ```

4. **同步渠道方式：**
   ```
   Mq消费同步渠道，例：
   topic:prod_insurance，tag:JD_Grp_policy_EarnCallBack，messageid:0AC110FC000105474C6C2D9B14701984，body:{"notifyType":"jdGrpPolicyEarnCallBack","earnTime":"2025-07-08","policyNo":"86000020251600002251"}
   ```

5. **同步渠道结果核实：**
   ```sql
   SELECT `is_send`,count(1) ,
   case when `is_send` = '0' then '未同步'
   when `is_send`  = '1' then '同步成功' end '同步结果'  
   FROM `ac_grp_son_account_his` WHERE `policy_no` in (SELECT policy_no FROM `ac_grp_policy` WHERE `channel_code` = 'RPM0011201' and `policy_status` = '0') and `earn_time` = '2025-07-08' GROUP BY `is_send` ;
   ```

## 三、支付宝资产同步

### （一）支付宝恒享长盈

1. **资产文件路径：**
   ```
   -- 明细文件
   /yunshare/edoc/Web/NewPolicyDetail/146/YY/mm/dd
   -- 资产文件
   /yunshare/edoc/Web/NewAccSync/146/YY/mm/dd
   ```

2. **交易中心kettle读取：**
   ```bash
   #支付宝恒享长盈资产同步、份额确认
   40 20 * * *  cd /yunshare/10-192-6-60/home/<USER>/pdi-ce-*******-371/data-integration/ && ./kitchen.sh -file=/yunshare/10-192-6-60/home/<USER>/pdi-ce-*******-371/kettle/com/huida/account/core/ali/newPolicyDetail.kjb -param active=prod >> /yunshare/10-192-6-60/logs/kettle/aliPolicyDetail/newPolicyDetail_$(date +"%Y-%m-%d_%H-%M-%S").log
   ```

3. **同步结果核实：**
   ```sql
   SELECT
   b.sync_earn_state,count(1),
   case when b.`sync_earn_state` = '0' then '未同步'
   when b.`sync_earn_state` = '1' then '已同步' end '同步状态'
   FROM
   prd_ac_policy a,
   son_account_his b
   WHERE
   a.policy_no = b.policy_no
   AND a.channel_code in ("mayibx","RPM0031601")
   AND b.sync_earn_state in ('0','1')
   AND b.earn_time = '2025-07-09'
   GROUP BY
   b.`sync_earn_state` ;
   ```

4. **同步渠道方式：**
   ```
   Mq消费同步，例：
   支付宝份额确认，保全回调渠道MQ的消息topic:prod_insurance,tag:ali_call_back,key:bb3f3028-a33b-44b6-8635-822e068a6553,msg：{"haveTb":"{\"amount\":181245,\"bizNo\":\"********1100300909720492117118\",\"changeDirection\":\"OUT\",\"changeShare\":\"1321.********\",\"changeType\":\"SURRENDER\",\"serviceCharge\":1831}","tbShare":"-1321.********","policyValue":"0","productCode":"mayibx_api_hxcy_zfb_hxcy","initFee":"2000","netValue":"1.********","tbType":"6","reqInfo":"{\"accruedProfitAmount\":24581,\"callBackUrl\":\"https://superlink.alipay.com/ANTVBX41/shareConfirm.htm\",\"firstCallBackFlag\":false,\"fiscalPeriod\":\"********\",\"netAssetValue\":\"1.********\",\"outPolicyNo\":\"86000020211601775770\",\"policyAccountValue\":0,\"policyNo\":\"21102885458251910972\",\"prevPeriodShare\":\"1321.********\",\"prevPolicyAccountValue\":183064,\"productCode\":\"mayibx_api_hxcy_zfb_hxcy\",\"profitAmount\":12,\"share\":\"0.********\",\"shareChangeDetailList\":[{\"amount\":181245,\"bizNo\":\"********1100300909720492117118\",\"changeDirection\":\"OUT\",\"changeShare\":\"1321.********\",\"changeType\":\"SURRENDER\",\"serviceCharge\":1831}]}","policyNo":"86000020211601775770","earnDate":"2025-07-09","tbEdorNo":"86110020254103535217","channelCode":"mayibx"}
   ```

5. **同步渠道结果核实：**
   ```sql
   SELECT
   b.`is_send` ,count(1),
   case
   when b.`is_send` = '0' then '未同步渠道'
   when b.`is_send` = '1' then '已同步渠道'
   end '同步渠道状态'
   FROM
   prd_ac_policy a,
   son_account_his b
   WHERE
   a.policy_no = b.policy_no
   AND a.channel_code in ("mayibx","RPM0031601")
   AND b.sync_earn_state in ('0','1')
   AND b.earn_time = '2025-07-09'
   GROUP BY
   b.`is_send` ;
   ```

### （二）小金余

1. **资产文件路径：**
   ```
   /yunshare/edoc/corefile/SinglePaymentCash/YY/mm/dd
   ```

2. **交易中心kettle读取：**
   ```bash
   #消费型终身寿现价同步（03:10:00）
   10 03 * * *  cd /yunshare/10-192-6-60/home/<USER>/pdi-ce-*******-371/data-integration/ && ./kitchen.sh -file=/yunshare/10-192-6-60/home/<USER>/pdi-ce-*******-371/kettle/com/huida/account/core/zssAssertSynch/zssAssertSynch.kjb -param:active=prod  >> /yunshare/10-192-6-60/logs/kettle/zssAssertSynch/zssAssertSynch_$(date +"%Y-%m-%d_%H-%M-%S").log
   ```

3. **同步渠道方式：**
   ```
   Mq消费同步渠道，例：
   【保单现价回流对接渠道】消费消息,次数:0,topic:prod_insurance,tag:policy_earn_reflux,messageId:0AC11072000177F03BB1344C86BA5BDA,body:{"accountId":1334085,"cashValue":5412.0000,"channelCode":"ali","createdTime":*************,"earnAmount":0.0000,"earnDate":*************,"incomeRecordId":********,"isDel":0,"isSend":0,"policyNo":"86000020211602773137","productCode":"ali_xjy_hyll","siteId":"S20201027000002","skuCode":"hyll_xjy","sumAccountEarn":536.0000,"tableIndex":"0001","updatedTime":*************,"userId":"3667081"}
   ```

4. **同步渠道结果核实：**
   ```sql
   SELECT count(1), 'ac_income_record_0001'  FROM `ac_income_record_0001` a , `t_cpy_policy` b WHERE a.site_id='S20201027000002' and  a.`earn_date` = '2025-07-10' and b.`status` = '5' and a.`policy_no` = b.`policy_no` and a.`is_send` = '0' union all
   SELECT count(1), 'ac_income_record_0002'  FROM `ac_income_record_0002` a , `t_cpy_policy` b WHERE a.site_id='S20201027000002' and  a.`earn_date` = '2025-07-10' and b.`status` = '5' and a.`policy_no` = b.`policy_no` and a.`is_send` = '0' union all
   SELECT count(1), 'ac_income_record_0003'  FROM `ac_income_record_0003` a , `t_cpy_policy` b WHERE a.site_id='S20201027000002' and  a.`earn_date` = '2025-07-10' and b.`status` = '5' and a.`policy_no` = b.`policy_no` and a.`is_send` = '0' union all
   SELECT count(1), 'ac_income_record_0004'  FROM `ac_income_record_0004` a , `t_cpy_policy` b WHERE a.site_id='S20201027000002' and  a.`earn_date` = '2025-07-10' and b.`status` = '5' and a.`policy_no` = b.`policy_no` and a.`is_send` = '0' union all
   SELECT count(1), 'ac_income_record_0005'  FROM `ac_income_record_0005` a , `t_cpy_policy` b WHERE a.site_id='S20201027000002' and  a.`earn_date` = '2025-07-10' and b.`status` = '5' and a.`policy_no` = b.`policy_no` and a.`is_send` = '0' union all
   SELECT count(1), 'ac_income_record_0006'  FROM `ac_income_record_0006` a , `t_cpy_policy` b WHERE a.site_id='S20201027000002' and  a.`earn_date` = '2025-07-10' and b.`status` = '5' and a.`policy_no` = b.`policy_no` and a.`is_send` = '0' union all
   SELECT count(1), 'ac_income_record_0007'  FROM `ac_income_record_0007` a , `t_cpy_policy` b WHERE a.site_id='S20201027000002' and  a.`earn_date` = '2025-07-10' and b.`status` = '5' and a.`policy_no` = b.`policy_no` and a.`is_send` = '0' union all
   SELECT count(1), 'ac_income_record_0008'  FROM `ac_income_record_0008` a , `t_cpy_policy` b WHERE a.site_id='S20201027000002' and  a.`earn_date` = '2025-07-10' and b.`status` = '5' and a.`policy_no` = b.`policy_no` and a.`is_send` = '0' union all
   SELECT count(1), 'ac_income_record_0009'  FROM `ac_income_record_0009` a , `t_cpy_policy` b WHERE a.site_id='S20201027000002' and  a.`earn_date` = '2025-07-10' and b.`status` = '5' and a.`policy_no` = b.`policy_no` and a.`is_send` = '0' union all
   SELECT count(1), 'ac_income_record_0010'  FROM `ac_income_record_0010` a , `t_cpy_policy` b WHERE a.site_id='S20201027000002' and  a.`earn_date` = '2025-07-10' and b.`status` = '5' and a.`policy_no` = b.`policy_no` and a.`is_send` = '0' ;
   ```

## 四、进核心数据监控

### （一）新单进核心

```sql
SELECT a.product_name 险种名称,
a.core_policy_status 推送核心失败状态,
a.policy_id policy_id,
a.order_no 订单号,
a.`status` 交易中心状态,
a.proposal_no 投保单号,
a.policy_no 保单号,
a.total_premium 金额,
a.vali_date 生效日,
a.create_time 创建时间 FROM `t_cpy_policy` a WHERE a.`status` = '5' and a.`core_policy_status` not in ('5','8','16');
```

### （二）保全未进核心

```sql
select a.id id, CONCAT(case when a.secur_type=0 then '02020902'
when a.secur_type=1 then '02020902'
when a.secur_type=2 then '02020902'
when a.secur_type=3 then '02030202'
when a.secur_type=4 then '02030不固定'
when a.secur_type=5 then '020301135'
when a.secur_type=6 then '020426'
when a.secur_type=7 then '020425'
when a.secur_type=8 then '02000602'
when a.secur_type=9 then '02000602' end,a.apply_proposal_no) 流水号,
case when a.secur_type=0 then '追加'
when a.secur_type=1 then '清偿'
when a.secur_type=2 then '清偿和追加'
when a.secur_type=3 then '贷款'
when a.secur_type=4 then '犹内退保'
when a.secur_type=5 then '退保'
when a.secur_type=6 then '加保'
when a.secur_type=7 then '减保'
when a.secur_type=8 then '犹内全额领取'
when a.secur_type=9 then '犹外全额领取' end 保全类型,
a.policy_no 保单号,a.apply_time 时间,a.amount 金额, a.apply_proposal_no 申请号
from t_cpy_secur_apply a where
a.advice_core_status !='1'
and a.trade_status='2'
and TIMESTAMPDIFF(MINUTE,a.apply_time,NOW())>270
ORDER BY a.apply_time,a.policy_no,a.secur_type ;
```

### （三）京东保全未回调渠道

```sql
SELECT case when a.secur_type=0 then '追加'
when a.secur_type=1 then '清偿'
when a.secur_type=2 then '清偿和追加'
when a.secur_type=3 then '贷款'
when a.secur_type=4 then '犹内退保'
when a.secur_type=5 then '退保'
when a.secur_type=6 then '加保'
when a.secur_type=7 then '减保'
when a.secur_type=8 then '犹内全额领取'
when a.secur_type=9 then '犹外全额领取' end 保全类型,
a.policy_no 保单号,a.apply_time 时间,a.amount 金额, a.apply_proposal_no 申请号,a.`call_back_status` 推送渠道状态,a.`call_back_channel_flag` 推送渠道标识
FROM `t_cpy_secur_apply` a WHERE a.`channel_code` = 'RPM0011201' and a.`trade_status` = '2' and (a.`call_back_status` != '1' or a.`call_back_status` is null) ;
```

## 五、核心利息同步

1. **利息同步路径：**
   ```
   /yunshare/edoc/Web/TLDailyFile/YY/mm/dd/TL/04/143
   ```

2. **交易中心kettle读取：**
   ```bash
   #核心利息同步（02:30:00）
   30 02 * * * cd /opt/kettle/pdi-ce-*******-371/data-integration/ && ./kitchen.sh -file=/opt/kettle/pdi-ce-*******-371/kettle/com/huida/account/core/interest_new/interest.kjb -param:active=prod >> /alilog/log/kettle/interest/interest_$(date +"%Y-%m-%d_%H-%M-%S").log
   ```

3. **同步结果核实：**
   ```sql
   SELECT
   a.`policy_no` 保单号,
   a.`edor_no` 贷款号,
   a.`channel_code` 渠道编码,
   b.`source_name` 渠道名称,
   a.`loan_date` 贷款时间
   FROM
   `account_loan_record` a
   LEFT JOIN `t_channel_source_set` b on a.`channel_code` = b.`source_code`
   WHERE
   a.`is_del` = '0'
   and a.`loan_status` = '0'
   and a.`channel_code` != 'ljs'
   and a.`created_time` < date(sysdate())
   and a.`edor_no` not in(
   SELECT
   loan_serial
   FROM
   tmp_account_loan_synch
   );
   ```