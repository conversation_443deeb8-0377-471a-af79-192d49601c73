监控项目阿里云部分梳理
概述
梳理核心部分逻辑，用于监控项目
1.阿里云销售渠道产品以及保单状态
涉及主要表：nb_policy,def_channel,def_channel_detail,pd_plan，pd_plan_acc
参考sql:
select
a.`Policy_No` 保单号,
a.`Channel_Type` 销售方式,
b.NAME 销售方式名称,
a.channel_detail 销售渠道,
c.name 销售渠道名称,
a.combination_code 产品sku,
d.`plan_name`  产品名称,
case when a.`policy_state`='0' then '未承保'
when a.`policy_state`='1' then '已承保'
when a.`policy_state`='2' then '转人工核保'
when a.`policy_state`='3' then '已退保'
when a.`policy_state`='4' then '伪删除_保单未投保成功'
when a.`policy_state`='5' and a.policy_no='86000020191300426752'   then '系统处理异常_核心承保'
when a.`policy_state`='5' and a.policy_no<>'86000020191300426752'  then '系统处理异常_核心终止' end 保单状态
from
nb_policy a,
def_channel b,
def_channel_detail c,
pd_plan   d
where
a.`Channel_Type` = b.`ID`
and c.`CHANNEL_TYPE` =b.`ID`
and a.`Channel_Detail` = c.id
and d.`plan_code` =a.combination_code
/*投连线校验sql,排除后为全部保单*/
and EXISTS ( select 1 from pd_plan_acc b where b.`plan_code` =a.combination_code and SUBSTR(b.`product_code`,3,1)=3)


1.1 保单状态码与状态含义的对应关系
无系统配置表，配置在代码中，代码部分截图


码值	标识	含义
0	POLICY_STATE_NOT_SIGN	未承保
1	POLICY_STATE_SIGN	已承保
2	POLICY_STATE_MANUAL	转人工核保
3	POLICY_STATE_CANCEL	已退保
E	POLICY_STATE_ERROR	系统处理异常
4	POLICY_STATE_DEL	伪删除
当前系统数据现状


2.梳理京东保单计价、结息、风保、资产推送逻辑
贷款利息结息，京东计价、赎回计价、资产批处理
执行顺序：凌晨贷款利息结息，当日净值公布后，先跑计价批，再跑赎回计价，最后跑资产推送，批处理执行截图（以2025-07-11日为例）



京东贷款结息：为发送MQ消息：
-- 贷款利息记录表
select * from base_api_track  a  where a.api='LoanCalInterestTask' and a.`R_Code` ='succ' and a.`Status` ='completed'  and a.in_date >CURDATE();
-- 今日贷款结息明细保单，一日一清，临时表，处理完毕贷款后放入
select a.`Channel_Detail`,count(1) from loan_suminterest_temp a GROUP BY a.`Channel_Detail` ;
select a.* from loan_suminterest_temp a GROUP BY a.`Channel_Detail` ;

-- 贷款总表
SELECT count(1)  FROM pos_loan a  WHERE a.Pay_Off_Flag = '0'    and a.Policy_status in ('0')    and a.Pay_Off_Flag = '0'  and a.Loan_Status = '2'   AND a.Interest_Date >=CURDATE();


京东计价数据同步中间表
阿里云计价前置处理，同步计价临时表：
把outside_property_jdjr_tmp_mq_use备份到outside_property_jdjr_tmp_mq_use_bak，然后删除了outside_property_jdjr_tmp_mq_use，然后再重新生成初始化的outside_property_jdjr_tmp_mq_use,
把outside_property_jdjr_mq_tmp备份到outside_property_jdjr_mq_bak，然后删除了outside_property_jdjr_mq_tmp

校验sql:select count(*) from outside_property_jdjr_tmp_mq_use where Send_State = '0';

/*outside_property_jdjr_tmp_mq_use计价前置抽数逻辑37渠道*/
insert into outside_property_jdjr_tmp_mq_use
(policy_No,
Acc_No,
Third_User_Id,
`state`,
third_order_no,
Prem,
Channel_Type,
Combination_Code,
Item_Id,
Product_Code,
Effect_Date,
Policy_State,
Update_Date,
Loan_Interest,
Loan_Total_Intererst,
Create_Date)
(select a.policy_No,Acc_No,(select b.Third_User_Id from nb_order b where a.policy_no = b.item),0,
(select b.third_order_no from nb_order b where a.policy_no = b.item),
(select p.Money from nb_policy p   where a.policy_no = p.policy_no) ,
(select p.Channel_Type from nb_policy p where  a.policy_no = p.policy_no),
(select  case  when nb.combination_code like 'sys%' then  (SELECT nbp.combination_code from nb_policy_old_plan  nbp where  nbp.Policy_No=a.Policy_No  )  else nb.combination_code  end
from nb_policy nb   where a.policy_no = nb.policy_no) ,
(select b.Item_Id from nb_order b where a.policy_no = b.item) ,
a.Product_Code,(select date(b.effect_date) from nb_policy b  where a.policy_no = b.policy_no) , '0',
DATE(CURDATE()),
(SELECT IFNULL((select loan_DayInterest FROM loan_suminterest_record_tmp WHERE policy_no = a.policy_no AND Cal_Date = DATE(CURDATE())),0)),
(SELECT IFNULL((select loan_TotalInterest FROM loan_suminterest_record_tmp WHERE policy_no = a.policy_no AND Cal_Date = DATE(CURDATE())),0)),
NOW()
from acc_main a,pd_product d
where a.product_type = #{productType,jdbcType=VARCHAR}
and a.found_date <![CDATA[<=]]> #{foundDate,jdbcType=TIMESTAMP}
and a.acc_state = '00'
and EXISTS (select 0 from fin_charge c where c.buss_no = a.policy_no and fin_type ='NB')
and exists (select 0 from nb_policy b where b.policy_no=a.policy_no and b.channel_detail and b.channel_detail = #{channelDetail ,jdbcType=VARCHAR})
and a.product_code = d.product_code and d.product_type = '01'
group by a.policy_no,a.acc_no)





公布净值同步阿里云后开始计价，扣除风险保费，生成京东资产
--计价临时表
select * from outside_property_jdjr_tmp_mq_use a where a.policy_no = '86000020211301000609' ;
--计价备份表
select a.`DealInfo` ,a.update_date,a.* from outside_property_jdjr_tmp_mq_use_bak a where a.policy_no = '86000020211301000609' ORDER BY  update_date DESC ;
--风险保费轨迹表 按新单,追加分别扣除风险保费(多条)
select * from acc_sa_risk_prem_trace a where a.`policy_no` ='86000020211301000609' ORDER BY  a.`happen_date` desc ;
--资产文件临时表
select * from outside_property_jdjr_mq_tmp a where a.policy_no = '86000020211301000609' ;
--资产文件备份表
select * from outside_property_jdjr_mq_bak a where a.policy_no = '86000020211301000609'  ORDER BY  update_date DESC;

35渠道
--资产文件临时表(计价也是这个)
select * from      outside_property_jd_tmp a where a.policy_no = '86000020181300110359' ;
--资产文件备份表
select * from      outside_property_jd_bak a where a.policy_no = '86000020181300110359'  ORDER BY  update_date DESC;


-- 京东资产文件目录：
-- 京东资产文件 jdjr:37渠道 jd：35渠道
select * from `outside_ftp`  a where a.`Ftp_Type`  in ('ftp.property.syn2jd','ftp.property.syn2jdjr');
35渠道: sftp/jd/upload/asset/
37渠道：sftp/jdjr/upload/asset




3.京东保全未进核心梳理
当前阿里云无新单，保全业务配置当前进核心保全配置为:LN,RF,SA,WT,CT,DA,RN,DP,每个文件最大1000笔数据，单次最多1000个文件
配置表：
-- 保全业务进核心配置
select * from def_kv where K ='pos.data.syn.check';
-- 抽取进核心数据条数限制
select * from def_kv where K ='data2core.limit';
-- 贷款与核心交互文件条数限制
select * from def_kv where K ='loan37';
-- 进核心批处理url配置
select * from def_kv where K in ('pos.posData.syn.CT','pos.posData.syn.DA','pos.posData.syn.DP','pos.posData.syn.LN','pos.posData.syn.RF','pos.posData.syn.RN','pos.posData.syn.SA','pos.posData.syn.WT')

业务表
-- 贷款表
select * from pos_loan;
-- 还款表
select * from pos_repay;
-- 其他保全表
select * from pos_item ;


阿里云保全未进核心统计sql
-- 核心统计sql
SELECT
'进核心：阿里云保全' as datano,
(
case
when count(1) > 700 then '异常'
else '成功'
end
) as valuekey,
'进核心：阿里云保全' as value1,
(
SELECT
CURRENT_DATE as value2
FROM
dual
),
count(1) as value3,
sum(A.money) as value4,
trunc(SYSDATE()) as value14
from
webfileservlog a
join lpjdtempedorapplyb b on a.otherno = b.otherno
and b.executestate <> '1'
where
a.executestate = '2'
and length(a.standby9) = '2'
and a.appdate <= trunc(sysdate)


4.非京东部分
-- 非京东计价
SELECT * FROM base_api_track a WHERE API in ('AccCalProfitOptTask','AccCalProfitTask') and a.`R_Code` ='succ' and a.`Status` ='completed'  and a.in_date >CURDATE() ;
-- 赎回计价
SELECT * FROM base_api_track a WHERE API in ('PosAccCalProfitTask') and a.`R_Code` ='succ' and a.`Status` ='completed'  and a.in_date >CURDATE() ;
-- 资产
SELECT * FROM base_api_track a WHERE API LIKE 'propertySyn4%' and a.`R_Code` ='succ' and a.`Status` ='completed'  and a.in_date >CURDATE() ;


5.保单监控汇总表

5.1 总表涉及字段
字段名	数据类型	描述
id	INT	自增序号，主键
monitor_date	DATE	监控日期
policy_no	VARCHAR(20)	保单号
policy_source	VARCHAR(50)	保单来源系统
channel	VARCHAR(50)	销售渠道
product_code	VARCHAR(20)	产品编码
product_name	VARCHAR(50)	产品名称
policy_status	VARCHAR(10)	保单状态
pricing_status	INT	计价状态：0 = 已计价，1 = 未计价
pricing_date	DATE	计价日期，最后一次完成计价的日期（如未计价则为 NULL）
risk_insurance_status	INT	风险保费扣除状态：0 = 已扣风保，1 = 未扣风保
risk_insurance_deduct_date	DATE	风险保费扣除日期，最后一次成功扣除风险保费的日期（未扣除则为 NULL）
interest_settlement_status	INT	贷款结息状态：0 = 已结息，1 = 未结息，2 = 无贷款无需结息
interest_settlement_date	DATE	结息日期，最后一次完成结息的日期（未结息或无需结息则为 NULL）
asset_push_status	INT	推送资产状态：0 = 已推送资产，1 = 未推送资产，2 = 无需推送资产（资产系统同步状态）
asset_push_date	DATE	资产推送日期，最后一次成功推送资产的日期（未推送或无需推送则为 NULL）

5.2 涉及主要表
nb_policy  
def_channel  
def_channel_detail  
pd_plan    
pd_plan_acc
outside_property_jdjr_tmp_mq_use
outside_property_jdjr_tmp_mq_use_bak
acc_sa_risk_prem_trace tmp
loan_suminterest_temp
pos_loan
outside_property_jdjr_mq_tmp
outside_property_jdjr_mq_bak
5.3 参考sql:
/*
35,37渠道统计sql：
*/
select
CURDATE() 监控日期,
a.`Policy_No` 保单号,
a.`Channel_Type` 销售方式,
b.NAME 销售方式名称,
a.channel_detail 销售渠道,
c.name 销售渠道名称,
a.combination_code 产品sku,
d.`plan_name`  产品名称,
case when a.`policy_state`='0' then '未承保'
when a.`policy_state`='1' then '已承保'
when a.`policy_state`='2' then '转人工核保'
when a.`policy_state`='3' then '已退保'
when a.`policy_state`='4' then '伪删除_保单未投保成功'
when a.`policy_state`='5' and a.policy_no='86000020191300426752'   then '系统处理异常_核心承保'
when a.`policy_state`='5' and a.policy_no<>'86000020191300426752'  then '系统处理异常_核心终止' end 保单状态 ,
case when a.channel_detail='37'  and EXISTS  (select 1
from acc_main ta,pd_product td
where ta.product_type ='01'
and ta.found_date <CURDATE()
and ta.acc_state = '00'
and EXISTS (select 0 from fin_charge tc where tc.buss_no = ta.policy_no and fin_type ='NB')
and exists (select 0 from nb_policy tb where tb.policy_no=ta.policy_no  and tb.channel_detail ='37')
and ta.product_code = td.product_code and td.product_type = '01' and ta.policy_no =a.policy_no)
and EXISTS  (select 1  from outside_property_jdjr_tmp_mq_use ta   where ta.policy_no = a.policy_no and ta.`Update_Date`=CURDATE() and ta.State='0') then   '未计价'
when a.channel_detail='37'  and   EXISTS  (select 1  from outside_property_jdjr_tmp_mq_use ta   where ta.policy_no = a.policy_no and ta.`Update_Date`=CURDATE() and ta.State='1' )  then '已计价'
when a.channel_detail='35'  and EXISTS  (select 1
from acc_main ta,pd_product td
where ta.product_type ='01'
and ta.found_date <CURDATE()
and ta.acc_state = '00'
and EXISTS (select 0 from fin_charge tc where tc.buss_no = ta.policy_no and fin_type ='NB')
and exists (select 0 from nb_policy tb where tb.policy_no=ta.policy_no  and tb.channel_detail ='35')
and ta.product_code = td.product_code and td.product_type = '01' and ta.policy_no =a.policy_no)
and not EXISTS  (select 1  from outside_property_jd_tmp ta   where ta.policy_no = a.policy_no and ta.`Update_Date`=CURDATE() ) then   '未计价'
when a.channel_detail='35'  and   EXISTS  (select 1  from outside_property_jd_tmp ta   where ta.policy_no = a.policy_no and ta.`Update_Date`=CURDATE() )  then '已计价'
else '无须计价' end 计价状态,
case when  a.channel_detail='37' then
case when   EXISTS (select 1 from outside_property_jdjr_tmp_mq_use tmp  where tmp.policy_no = a.policy_no and tmp.State=1)
then (select max(update_date) from outside_property_jdjr_tmp_mq_use tmp  where tmp.policy_no = a.policy_no and tmp.State=1)
else (select max(update_date) from outside_property_jdjr_tmp_mq_use_bak tmp  where tmp.policy_no = a.policy_no and tmp.State=1) end
when  a.channel_detail='35' then
case when EXISTS  (select 1 from outside_property_jd_tmp ta   where ta.policy_no = a.policy_no and ta.`Update_Date`=CURDATE() )
then CURDATE() ELSE  (select max(update_date) from outside_property_jd_bak tmp  where tmp.policy_no = a.policy_no) end end  计价日期,
case when not  EXISTS (select 1 from acc_sa_risk_prem_trace tmp  where tmp.policy_no = a.policy_no ) then '无风险保费'
when   EXISTS (select 1 from acc_sa_risk_prem_trace tmp  where tmp.policy_no = a.policy_no ) and not EXISTS (select 1 from acc_sa_risk_prem_trace tmp  where tmp.policy_no = a.policy_no and tmp.happen_date>=CURDATE()) then '未扣风保'
when  EXISTS (select 1 from acc_sa_risk_prem_trace tmp  where tmp.policy_no = a.policy_no and tmp.happen_date>=CURDATE()) then '已扣风保' end 风险保费扣除状态,
(select  DATE_FORMAT(max(tmp.happen_date), '%Y-%m-%d')  from acc_sa_risk_prem_trace tmp  where tmp.policy_no = a.policy_no) 风险保费扣除日期,
case when EXISTS  (select 1 from  loan_suminterest_temp tmp where tmp.policy_no=a.policy_no) then '已结息'
when EXISTS (SELECT 1  FROM pos_loan tmp  WHERE tmp.Pay_Off_Flag = '0'    and tmp.Policy_status in ('0')    and tmp.Pay_Off_Flag = '0'  and tmp.Loan_Status = '2' and tmp.policy_no=a.policy_no)
and not  EXISTS  (select 1 from  loan_suminterest_temp tmp where tmp.policy_no=a.policy_no) then '未结息'
else '无贷款无需结息' end 贷款结息状态,
(select max(tmp.`Interest_Time`) from  loan_suminterest_temp tmp where tmp.policy_no=a.policy_no) 结息日期,
case when   a.`policy_state`<>1 then '无需推送资产'
when   a.channel_detail='37' then
case when  EXISTS  (select 1 from outside_property_jdjr_mq_tmp tmp where tmp.policy_no = a.policy_no) then '已推送资产'        
when  a.`policy_state`=1 and not EXISTS  (select 1 from outside_property_jdjr_mq_tmp tmp where tmp.policy_no = a.policy_no) then '未推送资产' end
when   a.channel_detail='35' then
case when  EXISTS  (select 1 from outside_property_jd_tmp tmp where tmp.policy_no = a.policy_no and   tmp.`Update_Date`=CURDATE()) then '已推送资产'  
when  a.`policy_state`=1 and not EXISTS  (select 1 from outside_property_jd_tmp tmp where tmp.policy_no = a.policy_no  and tmp.`Update_Date`=CURDATE()) then '未推送资产' end end  推送资产状态,
case when a.channel_detail='37' then
case when  EXISTS  (select 1 from outside_property_jdjr_mq_tmp tmp where tmp.policy_no = a.policy_no)
then  (select tmp.Update_Date from outside_property_jdjr_mq_tmp tmp where tmp.policy_no = a.policy_no)
else (select max(tmp.`Update_Date`) from outside_property_jdjr_mq_bak tmp where tmp.policy_no = a.policy_no )  end
when a.channel_detail='35' then
case when  EXISTS  (select 1 from outside_property_jd_tmp tmp where tmp.policy_no = a.policy_no and   tmp.`Update_Date`=CURDATE()) then CURDATE()
else (select max(tmp.`Update_Date`) from outside_property_jd_bak tmp where tmp.policy_no = a.policy_no )  end end 资产推送日期          
from
nb_policy a,
def_channel b,
def_channel_detail c,
pd_plan   d
where
a.policy_no  in ('86000020211301000609','86000020191300317891','86000020181300110359')
and  a.`Channel_Type` = b.`ID`
and c.`CHANNEL_TYPE` =b.`ID`
and a.`Channel_Detail` = c.id
and d.`plan_code` =a.combination_code
/*投连线校验sql,排除后为全部保单*/
and EXISTS ( select 1 from pd_plan_acc b where b.`plan_code` =a.combination_code and SUBSTR(b.`product_code`,3,1)=3);



6.保单监控明细表
6.1明细表涉及字段
字段名	数据类型	描述
id	INT	自增序号，主键
monitor_date	DATE	监控日期
policy_no	VARCHAR(20)	保单号
policy_source	VARCHAR(50)	保单来源系统
channel	VARCHAR(50)	销售渠道
product_code	VARCHAR(20)	产品编码
product_name	VARCHAR(50)	产品名称
policy_status	VARCHAR(10)	保单状态
pricing_status	INT	计价状态：0 = 已计价，1 = 未计价
expected_pricing_cnt	INT	应计价条数
actual_pricing_cnt	INT	实际计价条数
pricing_date	DATE	计价日期，最后一次完成计价的日期（如未计价则为 NULL）
risk_insurance_status	INT	风险保费扣除状态：0 = 已扣风保，1 = 未扣风保
risk_insurance_deduct_date	DATE	风险保费扣除日期，最后一次成功扣除风险保费的日期（未扣除则为 NULL）
interest_settlement_status	INT	贷款结息状态：0 = 已结息，1 = 未结息，2 = 无贷款无需结息
expected_interest_cnt	INT	应结息条数
actual_interest_cnt	INT	实际结息条数
interest_settlement_date	DATE	结息日期，最后一次完成结息的日期（未结息或无需结息则为 NULL）
asset_push_status	INT	推送资产状态：0 = 已推送资产，1 = 未推送资产，2 = 无需推送资产（资产系统同步状态）
asset_push_date	DATE	资产推送日期，最后一次成功推送资产的日期（未推送或无需推送则为 NULL）
asset_push_method	INT	资产推送方式，0 = 接口推送，1 = 文件推送
asset_file_path	VARCHAR(100)	资产文件路径
asset_file_name	VARCHAR(100)	资产文件名
account_value	DECIMAL(18,2)	账户价值（保留 2 位小数，单位：元）
accumulated_income	DECIMAL(18,2)	累计收益（保留 2 位小数，单位：元）
daily_income	DECIMAL(18,2)	每日收益（保留 2 位小数，单位：元）
6.2 明细表涉及表

     nb_policy  
     def_channel  
     def_channel_detail  
     pd_plan    
     pd_plan_acc
     outside_property_jdjr_tmp_mq_use
     outside_property_jdjr_tmp_mq_use_bak
     acc_sa_risk_prem_trace tmp
     loan_suminterest_temp
     pos_loan
     outside_property_jdjr_mq_tmp
     outside_property_jdjr_mq_bak
     outside_property_jd_tmp
     outside_property_jd_bak
          fin_charge
     acc_main
     acc_income

6.3 明细表参考sql
/*
35,37渠道统计sql：
*/

select
CURDATE() 监控日期,
a.`Policy_No` 保单号,
a.`Channel_Type` 销售方式,
b.NAME 销售方式名称,
a.channel_detail 销售渠道,
c.name 销售渠道名称,
a.combination_code 产品sku,
d.`plan_name`  产品名称,
case when a.`policy_state`='0' then '未承保'
when a.`policy_state`='1' then '已承保'
when a.`policy_state`='2' then '转人工核保'
when a.`policy_state`='3' then '已退保'
when a.`policy_state`='4' then '伪删除_保单未投保成功'
when a.`policy_state`='5' and a.policy_no='86000020191300426752'   then '系统处理异常_核心承保'
when a.`policy_state`='5' and a.policy_no<>'86000020191300426752'  then '系统处理异常_核心终止' end 保单状态 ,
case when a.channel_detail='37'  and EXISTS  (select 1
from acc_main ta,pd_product td
where ta.product_type ='01'
and ta.found_date <CURDATE()
and ta.acc_state = '00'
and EXISTS (select 0 from fin_charge tc where tc.buss_no = ta.policy_no and fin_type ='NB')
and exists (select 0 from nb_policy tb where tb.policy_no=ta.policy_no  and tb.channel_detail ='37')
and ta.product_code = td.product_code and td.product_type = '01' and ta.policy_no =a.policy_no)
and EXISTS  (select 1  from outside_property_jdjr_tmp_mq_use ta   where ta.policy_no = a.policy_no and ta.`Update_Date`=CURDATE() and ta.State='0') then   '未计价'
when a.channel_detail='37'  and   EXISTS  (select 1  from outside_property_jdjr_tmp_mq_use ta   where ta.policy_no = a.policy_no and ta.`Update_Date`=CURDATE() and ta.State='1' )  then '已计价'
when a.channel_detail='35'  and EXISTS  (select 1
from acc_main ta,pd_product td
where ta.product_type ='01'
and ta.found_date <CURDATE()
and ta.acc_state = '00'
and EXISTS (select 0 from fin_charge tc where tc.buss_no = ta.policy_no and fin_type ='NB')
and exists (select 0 from nb_policy tb where tb.policy_no=ta.policy_no  and tb.channel_detail ='35')
and ta.product_code = td.product_code and td.product_type = '01' and ta.policy_no =a.policy_no)
and not EXISTS  (select 1  from outside_property_jd_tmp ta   where ta.policy_no = a.policy_no and ta.`Update_Date`=CURDATE() ) then   '未计价'
when a.channel_detail='35'  and   EXISTS  (select 1  from outside_property_jd_tmp ta   where ta.policy_no = a.policy_no and ta.`Update_Date`=CURDATE() )  then '已计价'
else '无须计价' end 计价状态,
(select count(distinct ta.acc_no)
from acc_main ta,pd_product td
where ta.product_type ='01'
and ta.found_date <CURDATE()
and ta.acc_state = '00'
and EXISTS (select 0 from fin_charge tc where tc.buss_no = ta.policy_no and fin_type ='NB')
and exists (select 0 from nb_policy tb where tb.policy_no=ta.policy_no  and tb.channel_detail =a.channel_detail)
and ta.product_code = td.product_code and td.product_type = '01' and ta.policy_no =a.policy_no)  应计价条数,
case when a.channel_detail='37' then  (select count(1)  from outside_property_jdjr_tmp_mq_use ta   where ta.policy_no = a.policy_no and ta.`Update_Date`=CURDATE() and ta.State='1' )
when a.channel_detail='35' then   (select count(1)  from outside_property_jd_tmp ta   where ta.policy_no = a.policy_no and ta.`Update_Date`=CURDATE() ) end  实际计价条数,  
case when  a.channel_detail='37' then
case when   EXISTS (select 1 from outside_property_jdjr_tmp_mq_use tmp  where tmp.policy_no = a.policy_no and tmp.State=1)
then (select max(update_date) from outside_property_jdjr_tmp_mq_use tmp  where tmp.policy_no = a.policy_no and tmp.State=1)
else (select max(update_date) from outside_property_jdjr_tmp_mq_use_bak tmp  where tmp.policy_no = a.policy_no and tmp.State=1) end
when  a.channel_detail='35' then
case when EXISTS  (select 1 from outside_property_jd_tmp ta   where ta.policy_no = a.policy_no and ta.`Update_Date`=CURDATE() )
then CURDATE() ELSE  (select max(update_date) from outside_property_jd_bak tmp  where tmp.policy_no = a.policy_no) end end  计价日期,

    case when not  EXISTS (select 1 from acc_sa_risk_prem_trace tmp  where tmp.policy_no = a.policy_no ) then '无风险保费'
         when   EXISTS (select 1 from acc_sa_risk_prem_trace tmp  where tmp.policy_no = a.policy_no ) and not EXISTS (select 1 from acc_sa_risk_prem_trace tmp  where tmp.policy_no = a.policy_no and tmp.happen_date>=CURDATE()) then '未扣风保'
         when  EXISTS (select 1 from acc_sa_risk_prem_trace tmp  where tmp.policy_no = a.policy_no and tmp.happen_date>=CURDATE()) then '已扣风保' end 风险保费扣除状态,
    (select  DATE_FORMAT(max(tmp.happen_date), '%Y-%m-%d')  from acc_sa_risk_prem_trace tmp  where tmp.policy_no = a.policy_no) 风险保费扣除日期,
    case when EXISTS  (select 1 from  loan_suminterest_temp tmp where tmp.policy_no=a.policy_no) then '已结息' 
         when EXISTS (SELECT 1  FROM pos_loan tmp  WHERE tmp.Pay_Off_Flag = '0'    and tmp.Policy_status in ('0')    and tmp.Pay_Off_Flag = '0'  and tmp.Loan_Status = '2' and tmp.policy_no=a.policy_no)
                    and not  EXISTS  (select 1 from  loan_suminterest_temp tmp where tmp.policy_no=a.policy_no) then '未结息'
         else '无贷款无需结息' end 贷款结息状态,
(SELECT case when count(1) =0  then '无需结息' else  count(1) end  FROM pos_loan ta  WHERE ta.Pay_Off_Flag = '0'    and ta.Policy_status in ('0')    and ta.Pay_Off_Flag = '0'  and ta.Loan_Status = '2'     and ta.`Policy_No` =a.`Policy_No`) 应结息条数,
(select count(1) from  loan_suminterest_temp tmp where tmp.policy_no=a.policy_no)  实际结息条数,
(select max(tmp.`Interest_Time`) from  loan_suminterest_temp tmp where tmp.policy_no=a.policy_no) 结息日期,

case when   a.`policy_state`<>1 then '无需推送资产'
when   a.channel_detail='37' then
case when  EXISTS  (select 1 from outside_property_jdjr_mq_tmp tmp where tmp.policy_no = a.policy_no) then '已推送资产'        
when  a.`policy_state`=1 and not EXISTS  (select 1 from outside_property_jdjr_mq_tmp tmp where tmp.policy_no = a.policy_no) then '未推送资产' end
when   a.channel_detail='35' then
case when  EXISTS  (select 1 from outside_property_jd_tmp tmp where tmp.policy_no = a.policy_no and   tmp.`Update_Date`=CURDATE()) then '已推送资产'  
when  a.`policy_state`=1 and not EXISTS  (select 1 from outside_property_jd_tmp tmp where tmp.policy_no = a.policy_no  and tmp.`Update_Date`=CURDATE()) then '未推送资产' end end  推送资产状态,

case when a.channel_detail='37' then
case when  EXISTS  (select 1 from outside_property_jdjr_mq_tmp tmp where tmp.policy_no = a.policy_no)
then  (select tmp.Update_Date from outside_property_jdjr_mq_tmp tmp where tmp.policy_no = a.policy_no)
else (select max(tmp.`Update_Date`) from outside_property_jdjr_mq_bak tmp where tmp.policy_no = a.policy_no )  end
when a.channel_detail='35' then
case when  EXISTS  (select 1 from outside_property_jd_tmp tmp where tmp.policy_no = a.policy_no and   tmp.`Update_Date`=CURDATE()) then CURDATE()
else (select max(tmp.`Update_Date`) from outside_property_jd_bak tmp where tmp.policy_no = a.policy_no )  end end 资产推送日期 ,
'文件' 资产推送方式，
case when  a.channel_detail='35' then  'sftp/jd/upload/asset/'
when  a.channel_detail='37' then  'sftp/jdjr/upload/asset/' end 资产文件路径,
'拼接' 资产文件名,
(select sum(ta.`acc_amount`)/100 from acc_main ta where ta.policy_no=a.policy_no )  账户价值,
(select sum(ta.`interest` )/100 from acc_main ta where ta.policy_no=a.policy_no ) 累计收益,
(select sum(ta.`daily_profit` )/100 from `acc_income`  ta where ta.policy_no=a.policy_no and ta.`happen_date` =CURDATE() )  当日收益
from
nb_policy a,
def_channel b,
def_channel_detail c,
pd_plan   d
where
a.policy_no  in ('86000020211301000609','86000020191300317891','86000020181300110359')
and  a.`Channel_Type` = b.`ID`
and c.`CHANNEL_TYPE` =b.`ID`
and a.`Channel_Detail` = c.id
and d.`plan_code` =a.combination_code
/*投连线校验sql,排除后为全部保单*/
and EXISTS ( select 1 from pd_plan_acc b where b.`plan_code` =a.combination_code and SUBSTR(b.`product_code`,3,1)=3);



附录
链接文档
保单监控系统文档
https://doc.weixin.qq.com/sheet/e3_AacA9gYiAEsCN6zsdtf8gQAmZw0aq?scode=ADUAZwe5ADsYGOjVw7AecA3ga9AC0&tab=BB08J2
添加注释
描述在文档里添加注释的方法，可使用高亮块的方式进行标注

相关参考文档
https://www.tapd.cn/42613457/markdown_wikis/show/#1142613457001000220
https://www.tapd.cn/42613457/markdown_wikis/show/#1142613457001000505

联系我们
如有其它问题，可联系文档编写人员@姜浩


