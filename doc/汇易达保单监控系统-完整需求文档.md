# 汇易达保单监控系统 - 完整需求文档

## 📋 项目概述

### 项目名称
汇易达保单监控系统 (Huida Policy Monitor Platform)

### 项目描述
基于RuoYi-Vue框架开发的保单资产监控系统，支持从FTP服务器自动下载保单资产文件，解析数据并进行监控分析。

### 技术架构
- **前端**: Vue.js + Element UI
- **后端**: Spring Boot + MyBatis
- **数据库**: StarRocks 3.3.11
- **缓存**: Redis
- **基础框架**: RuoYi-Vue 3.9.0

## 🎯 功能需求

### 1. 基础系统功能 (基于RuoYi)
- ✅ **用户管理**: 用户增删改查、权限分配
- ✅ **角色管理**: 角色定义、权限配置
- ✅ **菜单管理**: 动态菜单、权限控制
- ✅ **部门管理**: 组织架构管理
- ✅ **岗位管理**: 岗位定义和分配
- ✅ **字典管理**: 系统字典维护
- ✅ **参数配置**: 系统参数设置
- ✅ **操作日志**: 用户操作记录
- ✅ **登录日志**: 登录行为记录

### 2. 保单监控核心功能
- 🔄 **FTP文件监控**: 自动从FTP服务器下载保单资产文件
- 📊 **数据解析**: 解析Excel/CSV格式的资产文件
- 💰 **资产明细管理**: 保单资产数据的增删改查
- 📈 **收益分析**: 账户价值、累计收益、每日收益统计
- ⚙️ **任务调度**: 定时任务配置和执行
- 🔧 **FTP配置**: FTP服务器连接参数管理

### 3. 数据监控功能
- 📅 **按日期监控**: 支持按监控日期查询资产数据
- 🔍 **保单号查询**: 支持按保单号精确查询
- 📊 **收益统计**: 日收益、累计收益、利息统计
- 📈 **趋势分析**: 资产价值变化趋势
- 🚨 **异常监控**: 数据异常告警

## 🗄️ 数据库设计

### 核心业务表

#### 1. 资产文件明细表 (asset_file_detail)
```sql
- id: 主键ID
- monitor_date: 监控日期
- asset_file_path: 资产文件路径
- asset_file_name: 资产文件名
- policy_no: 保单号
- account_value: 账户价值
- accumulated_income: 累计收益
- daily_income: 每日收益
- daily_interest: 当日利息
- accumulated_interest: 累计利息
- bonus_income: 加息收益
```

#### 2. 保单FTP信息表 (policy_ftp_info)
```sql
- id: 主键ID
- policy_no: 保单号
- ftp_host: FTP服务器地址
- ftp_port: FTP端口
- ftp_username: FTP用户名
- ftp_password: FTP密码
- ftp_path: FTP文件路径
- status: 状态
```

#### 3. 监控任务配置表 (monitor_task_config)
```sql
- id: 任务ID
- task_name: 任务名称
- task_type: 任务类型
- cron_expression: Cron表达式
- status: 状态
- config_json: 任务配置JSON
```

### 系统基础表 (RuoYi标准表)
- sys_user: 用户信息表
- sys_role: 角色信息表
- sys_menu: 菜单权限表
- sys_dept: 部门表
- sys_post: 岗位表
- sys_config: 参数配置表
- sys_dict_type: 字典类型表
- sys_dict_data: 字典数据表
- sys_oper_log: 操作日志表
- sys_logininfor: 登录日志表

## 🔧 环境配置

### 数据库配置
```yaml
# StarRocks数据库配置
spring:
  datasource:
    druid:
      master:
        url: ***************************************************************************************************************************************************************************************************************
        username: huida_monitor
        password: Mm=5f~@di#r0
        driver-class-name: com.mysql.cj.jdbc.Driver
```

### Redis配置
```yaml
# Redis配置
spring:
  redis:
    host: redis-1p06t33wig.internal.res.idadt.com
    port: 6379
    password: 
    database: 0
```

## 🚀 部署说明

### 1. 数据库初始化
```bash
# 执行初始化脚本
mysql -h *********** -P 32501 -u huida_monitor -p < sql/ruoyi_starrocks_init.sql
```

### 2. 后端启动
```bash
# 编译项目
mvn clean package -DskipTests

# 启动后端服务
java -jar ruoyi-admin/target/ruoyi-admin.jar --spring.profiles.active=druid --server.port=8080
```

### 3. 前端启动
```bash
# 安装依赖
cd ruoyi-ui
npm install

# 启动开发服务器
npm run dev
```

## 👤 默认账号

### 管理员账号
- **用户名**: admin
- **密码**: admin123
- **权限**: 超级管理员，拥有所有功能权限

### 普通用户账号
- **用户名**: ry
- **密码**: admin123
- **权限**: 普通用户，基础查询权限

## 📊 系统访问

### 访问地址
- **前端地址**: http://localhost:80
- **后端地址**: http://localhost:8080
- **API文档**: http://localhost:8080/swagger-ui.html
- **数据库监控**: http://localhost:8080/druid

## 🔄 开发计划

### Phase 1: 基础框架 ✅
- [x] RuoYi-Vue框架搭建
- [x] StarRocks数据库适配
- [x] 基础系统功能验证

### Phase 2: 核心功能开发 🔄
- [ ] FTP文件下载功能
- [ ] 文件解析功能
- [ ] 资产数据管理
- [ ] 任务调度功能

### Phase 3: 监控分析 📋
- [ ] 数据统计分析
- [ ] 趋势图表展示
- [ ] 异常监控告警
- [ ] 报表导出功能

### Phase 4: 优化完善 📋
- [ ] 性能优化
- [ ] 安全加固
- [ ] 用户体验优化
- [ ] 文档完善

## 📝 注意事项

1. **数据库兼容性**: StarRocks兼容MySQL协议，但某些语法需要适配
2. **分区策略**: 资产明细表按月分区，提高查询性能
3. **数据安全**: FTP密码等敏感信息需要加密存储
4. **任务调度**: 使用Quartz实现定时任务调度
5. **异常处理**: 完善的异常处理和日志记录机制

## 🆘 技术支持

如有问题，请联系开发团队或查看相关文档。
