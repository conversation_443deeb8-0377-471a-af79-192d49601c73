# 汇易达保单监控系统开发文档

## 1. 项目结构说明

### 1.1 后端项目结构

```
huida-monitor-platform
├── doc                           # 项目文档目录
├── monit-backend                 # 后端项目根目录
│   ├── database                  # 数据库脚本目录
│   │   ├── init_ddl.sql          # 数据库表结构定义
│   │   └── init_dml.sql          # 数据库初始化数据
│   ├── huida-admin               # 主项目入口模块
│   │   ├── src                   # 源代码目录
│   │   │   └── main              # 主代码目录
│   │   │       ├── java          # Java源代码
│   │   │       │   └── com.huida.admin
│   │   │       │       └── Application.java  # 启动类
│   │   │       └── resources     # 资源文件目录
│   │   │           ├── application.yml       # 主配置文件
│   │   │           ├── application-dev.yml   # 开发环境配置
│   │   │           ├── application-prod.yml  # 生产环境配置
│   │   │           ├── static                # 静态资源
│   │   │           └── templates             # 模板文件
│   │   └── pom.xml               # Maven配置文件
│   ├── huida-common              # 通用工具模块
│   │   └── pom.xml
│   ├── huida-framework           # 核心框架模块
│   │   └── pom.xml
│   ├── huida-generator           # 代码生成模块
│   │   └── pom.xml
│   ├── huida-quartz              # 定时任务模块
│   │   └── pom.xml
│   └── huida-system              # 系统管理模块
│       └── pom.xml
├── DEPLOYMENT.md                 # 部署说明文档
├── README.md                     # 项目说明文档
└── pom.xml                       # 根项目Maven配置
```

### 1.2 前端项目结构

```
monitor-front
├── public                        # 公共文件目录
│   ├── index.html                # 主页面模板
│   └── favicon.ico               # 网站图标
├── src                           # 源代码目录
│   ├── api                       # 接口调用封装
│   ├── assets                    # 静态资源文件
│   ├── components                # 公共组件
│   ├── layout                    # 页面布局
│   ├── router                    # 路由配置
│   ├── store                     # 状态管理
│   ├── utils                     # 工具函数
│   ├── views                     # 页面视图
│   ├── App.vue                   # 根组件
│   └── main.js                   # 入口文件
├── .env.development              # 开发环境变量
├── .env.production               # 生产环境变量
├── babel.config.js               # Babel配置
├── package.json                  # 项目配置
├── vue.config.js                 # Vue配置
└── README.md                     # 前端项目说明
```

## 2. 开发环境搭建

### 2.1 后端开发环境

#### 2.1.1 JDK安装
1. 下载JDK 1.8+
2. 配置环境变量JAVA_HOME
3. 验证安装：
   ```bash
   java -version
   javac -version
   ```

#### 2.1.2 Maven安装
1. 下载Maven 3.0+
2. 配置环境变量MAVEN_HOME
3. 验证安装：
   ```bash
   mvn -version
   ```

#### 2.1.3 IDE配置
推荐使用IntelliJ IDEA进行开发：
1. 安装IntelliJ IDEA 2020.3+
2. 安装Lombok插件
3. 安装MyBatis插件
4. 配置Maven设置

#### 2.1.4 数据库配置
1. 安装StarRocks数据库
2. 创建开发数据库：
   ```sql
   CREATE DATABASE huida_monitor_dev;
   ```
3. 执行初始化脚本：
   ```bash
   mysql -u root -p huida_monitor_dev < monit-backend/database/init_ddl.sql
   mysql -u root -p huida_monitor_dev < monit-backend/database/init_dml.sql
   ```

### 2.2 前端开发环境

#### 2.2.1 Node.js安装
1. 下载Node.js 12.0+
2. 验证安装：
   ```bash
   node --version
   npm --version
   ```

#### 2.2.2 IDE配置
推荐使用Visual Studio Code进行开发：
1. 安装Visual Studio Code
2. 安装Vue插件
3. 安装ESLint插件
4. 安装Prettier插件

## 3. 编码规范

### 3.1 Java编码规范

#### 3.1.1 命名规范
- 类名：大驼峰命名法（如：UserService）
- 方法名：小驼峰命名法（如：getUserInfo）
- 变量名：小驼峰命名法（如：userName）
- 常量名：全大写加下划线（如：MAX_SIZE）
- 包名：全小写（如：com.huida.system）

#### 3.1.2 注释规范
- 类注释：使用/** */格式，说明类的功能和作者
- 方法注释：使用/** */格式，说明方法功能、参数和返回值
- 行内注释：使用//格式，说明代码逻辑

#### 3.1.3 代码格式
- 缩进：使用4个空格
- 括号：左大括号不换行
- 行宽：不超过120个字符

### 3.2 Vue编码规范

#### 3.2.1 命名规范
- 组件名：大驼峰命名法（如：UserList）
- 文件名：小写加连字符（如：user-list.vue）
- 变量名：小驼峰命名法（如：userInfo）

#### 3.2.2 组件结构
```vue
<template>
  <!-- 模板内容 -->
</template>

<script>
export default {
  name: 'ComponentName',
  components: {},
  props: {},
  data() {
    return {}
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {}
}
</script>

<style scoped>
/* 样式内容 */
</style>
```

## 4. 数据库开发规范

### 4.1 表结构设计规范

#### 4.1.1 字段命名
- 使用小写字母和下划线分隔（如：user_name）
- 主键字段统一命名为id
- 时间字段包含业务含义（如：create_time、update_time）
- 逻辑删除字段统一使用is_deleted（0=未删除，1=已删除）

#### 4.1.2 字段类型
- 主键：bigint(20)
- 字符串：varchar(N)
- 整数：int(11)
- 小数：decimal(M,D)
- 时间：datetime
- 布尔：tinyint(1)

### 4.2 SQL编写规范

#### 4.2.1 MyBatis映射文件
- SQL映射文件放置在resources/mapper目录下
- 文件命名与实体类对应（如：UserMapper.xml）
- 复杂查询使用<select>标签定义

#### 4.2.2 SQL语句
- 表名和字段名使用反引号包围
- SQL关键字全部大写
- 条件查询使用<where>标签
- 循环操作使用<foreach>标签

## 5. 接口开发规范

### 5.1 RESTful API设计规范

#### 5.1.1 URI设计
- 使用名词复数形式（如：/api/v1/users）
- 使用连字符分隔单词（如：/api/v1/user-profiles）
- 使用小写字母

#### 5.1.2 HTTP动词
- GET：获取资源
- POST：创建资源
- PUT：更新资源
- DELETE：删除资源

#### 5.1.3 返回格式
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {}
}
```

### 5.2 接口文档规范

#### 5.2.1 接口注释
使用Swagger注解描述接口：
```java
@ApiOperation("获取用户列表")
@GetMapping("/users")
public ResponseEntity listUsers(
    @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
    @ApiParam("每页数量") @RequestParam(defaultValue = "10") Integer pageSize) {
    // 接口实现
}
```

## 6. 代码提交规范

### 6.1 Git分支管理

#### 6.1.1 分支命名
- 主分支：master
- 开发分支：develop
- 功能分支：feature/功能名称
- 修复分支：fix/问题编号
- 发布分支：release/版本号

#### 6.1.2 提交信息规范
- feat：新增功能
- fix：修复bug
- docs：文档更新
- style：代码格式调整
- refactor：代码重构
- test：测试用例
- chore：构建过程或辅助工具的变动

### 6.2 代码审查

#### 6.2.1 审查要点
- 代码是否符合编码规范
- 功能实现是否正确
- 是否有潜在的性能问题
- 是否有安全漏洞
- 单元测试是否完整

## 7. 单元测试规范

### 7.1 测试框架
- 后端：JUnit 5 + Mockito
- 前端：Jest + Vue Test Utils

### 7.2 测试覆盖率
- 核心业务逻辑覆盖率要求达到80%以上
- 控制器层测试覆盖率要求达到70%以上
- 服务层测试覆盖率要求达到85%以上

### 7.3 测试编写规范
```java
@ExtendWith(MockitoExtension.class)
class UserServiceTest {
    
    @Mock
    private UserMapper userMapper;
    
    @InjectMocks
    private UserService userService;
    
    @Test
    @DisplayName("根据ID获取用户信息")
    void testGetUserById() {
        // 准备测试数据
        Long userId = 1L;
        User expectedUser = new User();
        expectedUser.setId(userId);
        expectedUser.setUserName("test");
        
        // 设置mock行为
        when(userMapper.selectById(userId)).thenReturn(expectedUser);
        
        // 执行测试
        User actualUser = userService.getUserById(userId);
        
        // 验证结果
        assertEquals(expectedUser, actualUser);
        verify(userMapper).selectById(userId);
    }
}
```

## 8. 代码生成使用指南

### 8.1 表结构要求
1. 表必须包含主键字段
2. 表名和字段名必须使用小写字母
3. 表名建议使用下划线分隔（如：user_info）

### 8.2 代码生成步骤
1. 在数据库中创建表结构
2. 在系统中配置数据源
3. 进入"系统工具"->"代码生成"模块
4. 选择需要生成代码的表
5. 配置生成参数
6. 执行代码生成

### 8.3 生成文件说明
- 后端：实体类、Mapper接口、Service类、Controller类
- 前端：Vue列表页面、表单页面、API接口封装

## 9. 定时任务开发

### 9.1 任务类开发
```java
@Component
public class SampleTask {
    
    @Scheduled(cron = "0 0 2 * * ?")
    public void execute() {
        // 任务逻辑
    }
}
```

### 9.2 任务配置
1. 在batch_task_config表中添加任务配置
2. 配置任务名称、类名、Cron表达式等信息
3. 在系统中启用任务

## 10. 日志规范

### 10.1 日志级别
- ERROR：系统错误、异常信息
- WARN：警告信息、潜在问题
- INFO：重要业务流程、状态变更
- DEBUG：调试信息、详细过程

### 10.2 日志格式
```java
private static final Logger logger = LoggerFactory.getLogger(UserService.class);

logger.info("用户{}登录系统，IP地址：{}", userName, ipAddr);
logger.error("获取用户信息失败，用户ID：{}", userId, e);
```

## 11. 异常处理规范

### 11.1 异常分类
- 业务异常：可预期的业务逻辑异常
- 系统异常：不可预期的系统级异常

### 11.2 异常处理
```java
try {
    // 业务逻辑
} catch (BusinessException e) {
    logger.warn("业务异常：{}", e.getMessage());
    return Result.fail(e.getMessage());
} catch (Exception e) {
    logger.error("系统异常：", e);
    return Result.error("系统异常，请联系管理员");
}
```

## 12. 性能优化建议

### 12.1 数据库优化
- 合理设计索引
- 避免SELECT *
- 使用分页查询
- 批量操作代替单条操作

### 12.2 缓存优化
- 合理使用Redis缓存热点数据
- 设置合适的过期时间
- 避免缓存穿透、雪崩、击穿

### 12.3 代码优化
- 避免重复计算
- 合理使用数据结构
- 减少对象创建
- 及时释放资源