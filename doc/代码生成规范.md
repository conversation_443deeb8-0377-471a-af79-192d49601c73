# 代码生成规范

## 1. 概述

基于若依架构生成代码，生成代码遵循以下规范：

## 2. 基础规范

### 2.1 项目编码
- 项目统一使用UTF-8编码

### 2.2 代码生成模块位置
- 所有后端代码生成在monit-backend文件夹中
- 通用功能放到huida-common中
- 非通用功能放到huida-admin中
- 定时任务或者批处理放到huida-quartz中
- 系统管理功能放到huida-system中
- 所有前端代码生成在monit-frontend文件夹中
- 前后端要完全分离可以独立部署，前后端用接口交互

## 3. 代码结构

### 3.1 后端代码结构
必须生成以下Java类：
- Controller：处理HTTP请求
- DTO：数据传输对象
- Entity：数据库实体
- Mapper：数据库操作接口和XML
- Query：查询参数对象
- Service：业务逻辑接口
- ServiceImpl：业务逻辑实现

### 3.2 前端代码结构
前端代码应按照以下结构组织：
- views：页面组件文件
  - 模块目录：按功能模块组织页面组件
  - 具体页面组件：每个页面对应一个Vue组件文件
- api：接口调用文件
  - 模块目录：按功能模块组织API调用
  - 具体API文件：每个模块对应一个API文件，包含该模块的所有接口调用
- components：通用组件文件
  - 公共组件：可复用的通用组件
- router：路由配置文件
  - index.js：路由配置主文件
- utils：工具类文件
  - 公共工具函数：可复用的工具函数
- assets：静态资源文件
  - 图片、样式等静态资源

## 4. 配置文件说明

### 4.1 配置文件结构
- 所有模块配置文件区分环境，配置文件放在resources目录下
- 文件名格式为`application-{profile}.yml`，如`application-dev.yml`
- 默认配置文件为`application.yml`
- 数据库和redis配置不单独创建配置文件，统一放到`application.yml`中

### 4.2 日志配置
- 日志统一采用log4j2，配置文件为`log4j2-spring.xml`
- 所有模块日志文件放在`logs`目录下
- 日志文件名格式为`{moduleName}-{profile}.log`，如`huida-admin-dev.log`

## 5. 命名规范

### 5.1 文件名规范
- Java类名采用大驼峰命名法，如`UserService`
- 包名采用小写字母命名，如`com.huida.system`
- 配置文件采用短横线分隔命名法，如`application-dev.yml`
- SQL映射文件采用与对应Mapper接口相同的名称，如`UserMapper.xml`

### 5.2 方法名规范
- 服务层方法名采用小驼峰命名法
- 查询方法以`select`、`get`、`find`、`query`等开头
- 插入方法以`insert`、`add`、`save`等开头
- 更新方法以`update`、`modify`、`change`等开头
- 删除方法以`delete`、`remove`等开头

## 6. 接口调用规范

### 6.1 RESTful API设计规范
- URI使用名词复数形式，如`/users`、`/orders`
- 使用HTTP动词表示操作类型：
  - GET：查询资源
  - POST：创建资源
  - PUT：更新资源
  - DELETE：删除资源
- 返回结果统一格式，包含状态码、消息和数据体

### 6.2 前后端接口规范
- 接口地址统一添加`/api`前缀
- 请求和响应数据格式统一使用JSON
- 统一错误处理机制
- 接口版本控制，通过URL路径或请求头实现

## 7. 数据库调用说明

### 7.1 MyBatis使用规范
- SQL映射文件与Mapper接口放在同一包下
- 使用Mapper接口注解或XML配置方式
- SQL语句中字段名使用数据库实际字段名
- 复杂查询使用 resultMap 进行映射

### 7.2 数据库操作规范
- 实体类字段与数据库字段保持一致
- 主键统一使用`id`字段名
- 创建时间和更新时间字段为`create_time`和`update_time`
- 逻辑删除字段为`del_flag`，0表示未删除，1表示已删除

## 8. 代码生成步骤

1. 在数据库中创建符合规范的数据表
2. 启动监控平台后台服务
3. 访问代码生成页面（通常在系统工具菜单下）
4. 导入需要生成代码的数据表
5. 根据需要配置生成选项
6. 执行代码生成操作
7. 下载生成的代码并集成到项目中

## 9. 注意事项

1. 代码生成器仅提供基础的CRUD功能代码，复杂的业务逻辑需要手动开发
2. 生成的代码可能需要根据项目实际情况进行调整
3. 不建议对生成的代码进行手动修改，以免在重新生成时丢失修改
4. 如需定制代码生成模板，可修改代码生成模块中的Velocity模板文件