# 汇易达保单监控系统设计文档

## 1. 系统架构设计

### 1.1 整体架构

汇易达保单监控系统采用前后端分离的架构设计：

```
┌─────────────────┐
│   前端展示层     │
├─────────────────┤
│   Web服务器层    │
├─────────────────┤
│   应用服务层     │
├─────────────────┤
│   数据访问层     │
├─────────────────┤
│   数据存储层     │
└─────────────────┘
```

### 1.2 技术架构

- **后端技术栈**：
  - Spring Boot 2.5.15
  - MyBatis + PageHelper
  - Spring Security + Shiro
  - StarRocks 2.0+（兼容MySQL协议）
  - Redis 5.0+
  - Druid 1.2.16
  - FastJSON 1.2.83
  - Quartz定时任务

- **前端技术栈**：
  - Vue.js 2.x
  - Element UI
  - Axios HTTP客户端
  - Vue Router
  - Vuex状态管理

### 1.3 部署架构

```
┌─────────────┐    ┌─────────────┐
│   Web浏览器  │    │  移动端应用  │
└──────┬──────┘    └──────┬──────┘
       │                  │
       └─────────┬────────┘
                 │
      ┌──────────▼──────────┐
      │   Nginx反向代理服务器 │
      └──────────┬──────────┘
                 │
      ┌──────────▼──────────┐
      │   应用服务器集群      │
      │  ┌────────────────┐ │
      │  │ Spring Boot应用 │ │
      │  └────────────────┘ │
      └──────────┬──────────┘
                 │
    ┌────────────▼────────────┐
    │        数据库服务器       │
    │  ┌────────────────────┐ │
    │  │     StarRocks      │ │
    │  └────────────────────┘ │
    │  ┌────────────────────┐ │
    │  │       Redis        │ │
    │  └────────────────────┘ │
    └─────────────────────────┘
```

## 2. 数据库设计

### 2.1 数据库选型

系统采用StarRocks作为主数据库，具有以下优势：
- 兼容MySQL协议，降低迁移成本
- 支持实时数据分析
- 高并发查询性能
- 支持分区和分桶优化

### 2.2 表结构设计

#### 2.2.1 业务表

1. **资产文件明细表(asset_file_detail)**
   - 按监控日期进行分区
   - 按保单号哈希分桶，16个桶
   - 副本数：3

2. **配置信息表(config_info)**
   - 存储系统各种配置信息
   - 副本数：3

3. **FTP信息表(ftp_info)**
   - 存储FTP连接相关信息
   - 副本数：3

4. **保单监控表(policy_monitor)**
   - 存储保单监控基本信息
   - 副本数：3

5. **保单监控明细表(policy_monitor_detail)**
   - 按监控日期进行分区
   - 按保单号哈希分桶，32个桶
   - 副本数：3

6. **监控汇总结果表(monitor_summary_result)**
   - 存储每日监控的汇总结果
   - 副本数：3

#### 2.2.2 系统管理表

1. **批处理任务配置表(batch_task_config)**
   - 配置和管理系统中的批处理任务
   - 副本数：3

2. **批处理任务日志表(batch_task_log)**
   - 记录批处理任务的执行日志
   - 副本数：3

3. **部门表(sys_dept)**
   - 存储系统组织架构中的部门信息
   - 副本数：3

4. **登录日志表(sys_logininfor)**
   - 记录用户登录系统的信息
   - 副本数：3

5. **菜单权限表(sys_menu)**
   - 存储系统菜单和权限信息
   - 副本数：3

6. **操作日志表(sys_oper_log)**
   - 记录用户在系统中的操作日志
   - 副本数：3

7. **角色信息表(sys_role)**
   - 存储系统中的角色信息
   - 副本数：3

8. **角色部门关联表(sys_role_dept)**
   - 存储角色和部门的关联关系
   - 副本数：3

9. **角色菜单关联表(sys_role_menu)**
   - 存储角色和菜单的关联关系
   - 副本数：3

10. **用户信息表(sys_user)**
    - 存储系统用户信息
    - 副本数：3

11. **用户角色关联表(sys_user_role)**
    - 存储用户和角色的关联关系
    - 副本数：3

### 2.3 分区策略

为提高查询性能，对大数据量表采用分区策略：
- **资产文件明细表**：按监控日期范围分区
- **保单监控明细表**：按监控日期范围分区

### 2.4 分桶策略

为优化数据分布和查询性能，对关键表采用分桶策略：
- **资产文件明细表**：按保单号哈希分桶，16个桶
- **保单监控明细表**：按保单号哈希分桶，32个桶

## 3. 模块设计

### 3.1 后端模块结构

```
huida-monitor-platform
├── huida-admin       # 主项目入口模块
├── huida-framework   # 核心框架模块
├── huida-system      # 系统管理模块
├── huida-common      # 通用工具模块
├── huida-quartz      # 定时任务模块
└── huida-generator   # 代码生成模块
```

### 3.2 核心模块功能

#### 3.2.1 huida-admin（主项目入口模块）
- 系统启动类
- 基础配置
- 全局异常处理

#### 3.2.2 huida-framework（核心框架模块）
- 权限控制
- 拦截器
- 全局异常处理
- 日志管理

#### 3.2.3 huida-system（系统管理模块）
- 用户管理
- 角色管理
- 菜单管理
- 部门管理
- 字典管理
- 通知公告
- 日志管理

#### 3.2.4 huida-common（通用工具模块）
- 工具类
- 常量定义
- 基础实体类
- 通用服务

#### 3.2.5 huida-quartz（定时任务模块）
- 任务调度
- 任务管理
- 任务日志

#### 3.2.6 huida-generator（代码生成模块）
- 代码生成器
- 模板引擎
- 生成配置

## 4. 接口设计

### 4.1 RESTful API设计规范

- URI使用名词复数形式（如：/api/v1/assets）
- HTTP动词使用规范：GET（获取）、POST（创建）、PUT（更新）、DELETE（删除）
- 返回结果格式统一，包含状态码、消息和数据
- 接口前缀统一为 `/api`
- 数据格式使用JSON
- 错误处理包含错误码和详细错误信息
- 接口版本控制使用 `/api/v1/`、`/api/v2/` 等形式

### 4.2 核心接口

#### 4.2.1 保单监控接口
- 获取保单监控列表：GET /api/v1/policy-monitor
- 获取保单监控详情：GET /api/v1/policy-monitor/{id}
- 新增保单监控：POST /api/v1/policy-monitor
- 更新保单监控：PUT /api/v1/policy-monitor/{id}
- 删除保单监控：DELETE /api/v1/policy-monitor/{id}

#### 4.2.2 监控统计接口
- 获取监控汇总结果：GET /api/v1/monitor-summary
- 获取监控明细：GET /api/v1/monitor-detail

#### 4.2.3 系统管理接口
- 用户管理接口：/api/v1/system/user
- 角色管理接口：/api/v1/system/role
- 菜单管理接口：/api/v1/system/menu
- 部门管理接口：/api/v1/system/dept

## 5. 安全设计

### 5.1 认证机制
- 基于Spring Security的用户认证
- JWT Token认证方式
- 用户名密码登录
- 登录状态管理

### 5.2 授权机制
- 基于RBAC的权限控制模型
- 菜单权限控制
- 按钮权限控制
- 数据权限控制

### 5.3 数据安全
- 敏感数据加密存储
- SQL注入防护
- XSS攻击防护
- CSRF攻击防护

## 6. 性能设计

### 6.1 数据库优化
- 合理设计索引
- 分区表提高查询性能
- 分桶优化数据分布
- 读写分离

### 6.2 缓存设计
- Redis缓存热点数据
- Session缓存
- 字典数据缓存
- 菜单权限缓存

### 6.3 异步处理
- 异步日志记录
- 异步任务处理
- 消息队列解耦

## 7. 部署设计

### 7.1 部署环境要求
- JDK 1.8+
- StarRocks 2.0+
- Maven 3.0+
- Node.js 12.0+
- npm 6.0+

### 7.2 部署流程
1. 数据库初始化
2. 后端服务部署
3. 前端页面部署
4. Nginx配置
5. 系统配置调整
6. 启动服务验证