# 汇易达保单监控系统功能文档

## 1. 系统概述

汇易达保单监控系统是一个基于若依框架构建的保单监控管理平台，采用前后端分离架构设计。系统主要用于监控保单的计价、风险保费扣除、结息、资产推送等业务流程的执行状态，支持实时监控、数据统计、预警通知等功能。

## 2. 核心功能模块

### 2.1 保单监控模块

保单监控模块是系统的核心功能，主要用于监控各类保单业务流程的执行状态。

#### 2.1.1 计价状态监控
- 监控保单计价执行情况
- 统计计价成功、失败、不涉及的保单数量
- 记录最后一次计价成功的日期

#### 2.1.2 风险保费状态监控
- 监控保单风险保费扣除情况
- 统计风险保费扣除成功、失败、不涉及的保单数量
- 记录最后一次风险保费扣除成功的日期

#### 2.1.3 结息状态监控
- 监控保单贷款结息执行情况
- 统计结息成功、失败、不涉及的保单数量
- 记录最后一次结息成功的日期

#### 2.1.4 资产推送状态监控
- 监控资产推送至官网、官微、交易系统、渠道等各平台的情况
- 统计各平台资产推送成功、失败、不涉及的保单数量
- 记录各平台最后一次资产推送成功的日期
- 记录各平台账户价值、累计收益、每日收益等数据

### 2.2 数据统计模块

数据统计模块用于对保单监控数据进行汇总分析，提供各类统计报表。

#### 2.2.1 监控汇总结果
- 按日期统计各类业务的总数量、成功数量、失败数量、不涉及数量
- 提供可视化图表展示统计数据

#### 2.2.2 保单明细查询
- 提供保单详细监控信息查询
- 支持按保单号、产品编码、销售渠道等条件筛选
- 支持导出查询结果

### 2.3 预警通知模块

预警通知模块用于在业务流程出现异常时及时通知相关人员。

#### 2.3.1 异常流程预警
- 自动检测未完成的业务流程
- 生成预警信息并通知相关人员
- 支持多种通知方式（邮件、短信等）

#### 2.3.2 预警日志管理
- 记录所有预警信息
- 支持预警信息查询和处理状态更新

### 2.4 系统管理模块

系统管理模块提供用户、角色、部门、菜单等系统基础功能管理。

#### 2.4.1 用户管理
- 用户信息维护
- 用户权限分配
- 用户状态管理

#### 2.4.2 角色管理
- 角色信息维护
- 角色权限分配
- 角色与用户关联管理

#### 2.4.3 部门管理
- 组织架构维护
- 部门信息管理

#### 2.4.4 菜单管理
- 系统菜单维护
- 菜单权限配置

### 2.5 系统监控模块

系统监控模块用于监控系统运行状态和批处理任务执行情况。

#### 2.5.1 操作日志
- 记录用户操作日志
- 支持操作日志查询和分析

#### 2.5.2 登录日志
- 记录用户登录信息
- 支持登录日志查询和分析

#### 2.5.3 批处理任务管理
- 定时任务配置和管理
- 任务执行日志查看
- 任务执行状态监控

### 2.6 系统工具模块

系统工具模块提供系统配置、代码生成等辅助功能。

#### 2.6.1 参数配置
- 系统参数维护
- 配置项状态管理

#### 2.6.2 代码生成
- 根据数据库表结构自动生成前后端代码
- 提高开发效率

## 3. 业务流程

### 3.1 数据采集流程
1. 系统从各业务系统获取保单数据
2. 解析资产文件并存储到资产文件明细表
3. 根据业务规则生成保单监控明细数据

### 3.2 监控分析流程
1. 系统定时执行监控任务
2. 对比应处理保单数与实际处理保单数
3. 生成监控汇总结果

### 3.3 预警通知流程
1. 系统检测到异常业务流程
2. 生成预警信息
3. 通过配置的通知方式发送预警

## 4. 权限控制

系统采用基于RBAC（Role-Based Access Control）模型的权限控制机制：
- 用户关联角色
- 角色关联菜单权限
- 支持细粒度权限控制（菜单级、按钮级）