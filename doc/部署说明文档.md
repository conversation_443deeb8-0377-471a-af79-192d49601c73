# 汇易达保单监控系统部署说明文档

## 1. 环境要求

### 1.1 基础环境
- **操作系统**：Linux/Windows Server
- **Java版本**：JDK 1.8+
- **Node.js版本**：14.0+
- **数据库**：StarRocks 2.5+（兼容MySQL协议）
- **Redis**：6.0+（可选，用于缓存）

### 1.2 服务器配置建议
- **CPU**：4核心以上
- **内存**：8GB以上
- **磁盘**：100GB以上可用空间
- **网络**：千兆网卡

## 2. 数据库部署

### 2.1 StarRocks数据库安装
```bash
# 下载StarRocks安装包
wget https://releases.starrocks.io/starrocks/StarRocks-2.5.4.tar.gz

# 解压安装包
tar -zxvf StarRocks-2.5.4.tar.gz

# 配置并启动StarRocks
cd StarRocks-2.5.4
# 按照官方文档配置fe.conf和be.conf
./bin/start_fe.sh --daemon
./bin/start_be.sh --daemon
```

### 2.2 数据库初始化
```bash
# 连接到StarRocks数据库
mysql -h localhost -P 9030 -u root

# 执行初始化脚本
source /path/to/huida_monitor_platform_init.sql
```

### 2.3 数据库连接配置
```yaml
# application-prod.yml
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      url: ***************************************************************************************************************************************************************
      username: root
      password: your_password
```

## 3. 后端部署

### 3.1 编译打包
```bash
# 进入项目根目录
cd huida-monitor-platform

# Maven编译打包
mvn clean package -DskipTests=true

# 打包完成后，jar包位于 huida-admin/target/huida-admin.jar
```

### 3.2 配置文件修改
```bash
# 修改配置文件
vim huida-admin/src/main/resources/application-prod.yml

# 主要配置项：
# 1. 数据库连接信息
# 2. Redis连接信息（如果使用）
# 3. 文件上传路径
# 4. 日志配置
```

### 3.3 启动后端服务
```bash
# 方式1：直接运行jar包
java -jar huida-admin/target/huida-admin.jar --spring.profiles.active=prod

# 方式2：使用systemd管理（推荐）
# 创建服务文件
sudo vim /etc/systemd/system/huida-monitor.service

# 服务文件内容：
[Unit]
Description=Huida Monitor Platform
After=network.target

[Service]
Type=simple
User=huida
ExecStart=/usr/bin/java -jar /opt/huida-monitor/huida-admin.jar --spring.profiles.active=prod
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target

# 启动服务
sudo systemctl enable huida-monitor
sudo systemctl start huida-monitor
```

## 4. 前端部署

### 4.1 编译打包
```bash
# 进入前端目录
cd huida-ui

# 安装依赖
npm install

# 修改API地址
vim .env.production
# VUE_APP_BASE_API = 'http://your-server-ip:8080'

# 编译打包
npm run build:prod
```

### 4.2 Nginx配置
```nginx
# /etc/nginx/sites-available/huida-monitor
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        root /var/www/huida-ui/dist;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    
    # 后端API代理
    location /dev-api/ {
        proxy_pass http://localhost:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 文件上传大小限制
    client_max_body_size 50M;
}

# 启用站点
sudo ln -s /etc/nginx/sites-available/huida-monitor /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 5. 系统配置

### 5.1 默认登录信息
- **用户名**：admin
- **密码**：admin123

### 5.2 首次登录后必须操作
1. 修改默认管理员密码
2. 配置FTP连接信息
3. 设置企微机器人Webhook地址
4. 配置批处理任务参数
5. 测试数据同步功能

### 5.3 FTP配置示例
```json
{
  "ftpName": "生产环境FTP",
  "ftpHost": "*************",
  "ftpPort": 21,
  "ftpUsername": "ftpuser",
  "ftpPassword": "ftppass123",
  "status": "1",
  "remark": "生产环境资产文件FTP服务器"
}
```

## 6. 监控与维护

### 6.1 日志查看
```bash
# 应用日志
tail -f /opt/huida-monitor/logs/huida-monitor.log

# 系统服务日志
sudo journalctl -u huida-monitor -f

# Nginx访问日志
tail -f /var/log/nginx/access.log
```

### 6.2 性能监控
- 访问 http://your-domain.com/druid 查看数据库连接池状态
- 访问 http://your-domain.com/actuator/health 查看应用健康状态
- 监控服务器CPU、内存、磁盘使用情况

### 6.3 数据备份
```bash
# 数据库备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/huida-monitor"
mkdir -p $BACKUP_DIR

# 导出数据
mysqldump -h localhost -P 9030 -u root -p huida_monitor_platform > $BACKUP_DIR/huida_monitor_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/huida_monitor_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete
```

## 7. 故障排查

### 7.1 常见问题
1. **数据库连接失败**
   - 检查StarRocks服务是否正常运行
   - 验证数据库连接参数
   - 检查网络连通性

2. **前端页面无法访问**
   - 检查Nginx配置是否正确
   - 验证静态文件路径
   - 查看Nginx错误日志

3. **API接口调用失败**
   - 检查后端服务是否正常运行
   - 验证API代理配置
   - 查看应用日志

### 7.2 性能优化
1. **数据库优化**
   - 定期分析表结构和索引使用情况
   - 配置合适的分区策略
   - 监控查询性能

2. **应用优化**
   - 调整JVM参数
   - 配置连接池大小
   - 启用缓存机制

## 8. 安全配置

### 8.1 网络安全
- 配置防火墙规则，只开放必要端口
- 使用HTTPS加密传输
- 配置访问白名单

### 8.2 应用安全
- 定期更新系统和依赖包
- 配置强密码策略
- 启用操作日志审计
- 定期备份重要数据

## 9. 升级说明

### 9.1 应用升级步骤
1. 备份当前版本和数据
2. 停止应用服务
3. 替换新版本文件
4. 执行数据库升级脚本（如有）
5. 启动应用服务
6. 验证功能正常

### 9.2 回滚方案
1. 停止新版本服务
2. 恢复旧版本文件
3. 恢复数据库备份（如需要）
4. 启动旧版本服务
5. 验证功能正常

## 10. 联系支持

如遇到部署问题，请联系技术支持：
- **邮箱**：<EMAIL>
- **电话**：400-xxx-xxxx
- **技术文档**：https://docs.huida.com
