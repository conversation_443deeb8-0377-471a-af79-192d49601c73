# 汇易达保单监控系统 - 系统梳理汇总文档

## 1. 系统概述

汇易达保单监控系统是一个基于若依框架构建的保单监控管理平台，采用前后端分离架构设计。系统用于监控保单的计价、风险保费扣除、结息、资产推送等业务流程的执行状态，支持实时监控、数据统计、预警通知等功能。

本系统主要依赖三个核心数据源：
1. 交易中心系统.md
2. 核心系统.md
3. 阿里云系统.md

以下是对这三个系统的详细梳理和汇总。

## 2. 交易中心系统

### 2.1 主要业务

交易中心系统主要负责京东资产同步，包括京东小金罐和京东T0两种产品。

#### 京东小金罐
- **资产文件路径**: `/yunshare/edoc/Web/NewPolicyAmnt/143/YY/mm/dd`
- **Kettle读取任务**:
  ```bash
  # 小金罐-核心收益同步(19:30:00)
  30 19 * * * cd /opt/kettle/pdi-ce-8.3.0.0-371/data-integration/ && ./kitchen.sh -file=/opt/kettle/pdi-ce-8.3.0.0-371/kettle/com/huida/account/core/assets/assets.kjb -param active=prod >> /alilog/log/kettle/newassets/newassets_$(date +"%Y-%m-%d_%H-%M-%S").log
  ```

#### 京东T0
- **资产文件路径**: `/yunshare/edoc/Web/NewPolicyAmnt/T0/YY/mm/dd`
- **Kettle读取任务**:
  ```bash
  # T0收益同步(20:20)
  20 20 * * *  cd /opt/kettle/pdi-ce-8.3.0.0-371/data-integration/ && ./kitchen.sh -file=/opt/kettle/pdi-ce-8.3.0.0-371/kettle/com/huida/synch/t0assets/assets.kjb -param active=prod >> /alilog/log/kettle/newassets/t0newassets_$(date +"%Y-%m-%d_%H-%M-%S").log
  ```

### 2.2 同步方式

交易中心通过MQ消息消费方式进行同步，例如：
```
topic:prod_callback_insurance,tag:trade_callback_new,messageid:0AC110FC000105474C6C234170435A47,body:{"notifyType":"policyEarnCallBack","earnTime":"2025-07-04","policyNo":"86000020221600005157"}
```

### 2.3 关键监控点

1. **资产同步状态监控**
2. **同步结果验证**（通过syncEarnState状态码）
   - 0: 未处理条数
   - 1: 处理成功条数
   - 2: 锁定中保单资产条数
   - 3: 多推送条数
   - 4: 其他产品条数
   - 5: 资产数量与账户不匹配条数
   - 6: 临时表数据异常条数
   - 7: 上个交易日未同步收益条数
   - 8: 其他异常条数
3. **渠道同步结果**（is_send字段）
   - 0: 未同步
   - 1: 同步成功

## 3. 核心系统

### 3.1 核心保单状态

核心系统管理所有保单的基础状态信息。

#### 涉及主要表
- lcpol
- lccont
- lccontstate

#### 保单状态码表
通过以下SQL获取状态码表信息：
```sql
-- 状态枚举值
select * from ldcode a where a.codetype = 'contavailablereason';
select * from ldcode a where a.codetype = 'contterminatereason';
select * from ldcode a where a.codetype = 'contavailablestate';
select * from ldcode a where a.codetype = 'contterminatestate';
```

#### 保单状态查询
```sql
SELECT  a."contno" 保单号, (SELECT  codename from  ldcode where codetype ='selltype'  and  code=a.selltype) 渠道 ,
CASE
WHEN a.appflag = '0' THEN '投保'
WHEN a.appflag = '1' THEN
DECODE((SELECT  DISTINCT 1  -- 确保只返回一个值
FROM lccontstate
WHERE statetype = 'Available'
AND contno = a.contno
AND polno = b.mainpolno
AND state = '1'
AND enddate IS NULL),
'1', '失效',
'承保')
ELSE
DECODE((SELECT MAX(statereason)  -- 使用MAX确保只返回一个值
FROM lccontstate
WHERE statetype = 'Terminate'
AND contno = a.contno
AND polno = b.mainpolno
AND state = '1'
AND enddate IS NULL),
'01', '满期终止',
'02', '退保终止',
'03', '解约终止',
'04', '理赔终止',
'05', '协退终止',
'06', '犹退终止',
'07', '失效终止',
'08', '其他终止')
END AS 保单状态
FROM lccont a
JOIN lcpol b ON a."contno" = b."contno" AND b."polno" = b."mainpolno"
WHERE a."appflag" IN ('1', '4')
AND SUBSTR(b.riskcode, 3, 1) = '3' and  a."contno" ='86000020211602304341';
```

### 3.2 核心T+2计价

#### 涉及主要表
- lcinsureacctrace

#### 抽数SQL
```sql
select a.polno
from lcpol a,lccont lc
where  a.contno = lc.contno
and exists (select 1
from lcinsureacctrace c
where c.state = '0'    --  未计价状态
and a.polno = c.polno
and c.shouldvaluedate =  to_date('2025-07-11','yyyy-MM-dd')   -- 应计价日期
and exists (select 1
from LOAccUnitPrice d
where d.startdate = c.paydate
and c.insuaccno = d.insuaccno
and d.state = '0'))   --  已公布对应的净值
and not exists
(select 1
from lcinsureacctrace c
where c.state = '0'
and a.polno = c.polno
and (c.unitcount <> 0 or c.money <> 0)   -- 账户金额或份额不等于0
and c.shouldvaluedate = to_date('2025-07-11','yyyy-MM-dd')
and not exists (select 1
from LOAccUnitPrice d
where d.startdate = c.paydate
and d.state = '0'
and c.insuaccno = d.insuaccno))
and not exists (select 1
from lcpol e, lpedoritem lp
where e.polno = a.polno
and e.polno = mainpolno
and e.contno = lp.contno
and e.uintlinkvaliflag = '4'
and lp.edortype = 'WT')  -- 去除犹豫期内不进账户，发生犹退的
and (lc.isPlanFLag <> '1' or lc.isPlanFLag is null)
and a.riskcode = '413211';
```

### 3.3 核心贷款结息

#### 涉及主要表
- lcloanaccclass

#### 查询SQL
```sql
select a."contno" ,b."selltype" ,  COUNT(1)
from lcloanaccclass a  join  lccont  b  on  a."contno" =b."contno"     
where   -- a.modifydate=TRUNC(sysdate-1)   and    -- 如果只统计增量，看昨天已结息的，今日未结的
a."baladate" <>TRUNC(sysdate-1) -- 所有未结息的
and a.payoffflag=0
GROUP BY a."contno",b."selltype"  ;
```

## 4. 阿里云系统

### 4.1 销售渠道产品及保单状态

#### 涉及主要表
- nb_policy
- def_channel
- def_channel_detail
- pd_plan
- pd_plan_acc

#### 参考SQL
```sql
select
a.`Policy_No` 保单号,
a.`Channel_Type` 销售方式,
b.NAME 销售方式名称,
a.channel_detail 销售渠道,
c.name 销售渠道名称,
a.combination_code 产品sku,
d.`plan_name`  产品名称,
case when a.`policy_state`='0' then '未承保'
when a.`policy_state`='1' then '已承保'
when a.`policy_state`='2' then '转人工核保'
when a.`policy_state`='3' then '已退保'
when a.`policy_state`='4' then '伪删除_保单未投保成功'
when a.`policy_state`='5' and a.policy_no='86000020191300426752'   then '系统处理异常_核心承保'
when a.`policy_state`='5' and a.policy_no<>'86000020191300426752'  then '系统处理异常_核心终止' end 保单状态
from
nb_policy a,
def_channel b,
def_channel_detail c,
pd_plan   d
where
a.`Channel_Type` = b.`ID`
and c.`CHANNEL_TYPE` =b.`ID`
and a.`Channel_Detail` = c.id
and d.`plan_code` =a.combination_code
/*投连线校验sql,排除后为全部保单*/
and EXISTS ( select 1 from pd_plan_acc b where b.`plan_code` =a.combination_code and SUBSTR(b.`product_code`,3,1)=3)
```

#### 保单状态码含义
| 码值 | 标识 | 含义 |
|------|------|------|
| 0 | POLICY_STATE_NOT_SIGN | 未承保 |
| 1 | POLICY_STATE_SIGN | 已承保 |
| 2 | POLICY_STATE_MANUAL | 转人工核保 |
| 3 | POLICY_STATE_CANCEL | 已退保 |
| E | POLICY_STATE_ERROR | 系统处理异常 |
| 4 | POLICY_STATE_DEL | 伪删除 |

### 4.2 京东保单计价、结息、风保、资产推送逻辑

#### 执行顺序
1. 凌晨贷款利息结息
2. 当日净值公布后，先跑计价批
3. 再跑赎回计价
4. 最后跑资产推送

#### 贷款利息结息
京东贷款结息通过发送MQ消息实现：
```sql
-- 贷款利息记录表
select * from base_api_track  a  where a.api='LoanCalInterestTask' and a.`R_Code` ='succ' and a.`Status` ='completed'  and a.in_date >CURDATE();

-- 今日贷款结息明细保单，一日一清，临时表，处理完毕贷款后放入
select a.`Channel_Detail`,count(1) from loan_suminterest_temp a GROUP BY a.`Channel_Detail` ;

-- 贷款总表
SELECT count(1)  FROM pos_loan a  WHERE a.Pay_Off_Flag = '0'    and a.Policy_status in ('0')    and a.Pay_Off_Flag = '0'  and a.Loan_Status = '2'   AND a.Interest_Date >=CURDATE();
```

#### 计价数据同步
阿里云计价前置处理，同步计价临时表：
```sql
-- 校验SQL
select count(*) from outside_property_jdjr_tmp_mq_use where Send_State = '0';
```

#### 计价后处理
计价完成后会扣除风险保费并生成京东资产：
- 计价临时表: outside_property_jdjr_tmp_mq_use
- 计价备份表: outside_property_jdjr_tmp_mq_use_bak
- 风险保费轨迹表: acc_sa_risk_prem_trace
- 资产文件临时表: outside_property_jdjr_mq_tmp
- 资产文件备份表: outside_property_jdjr_mq_bak

### 4.3 保全未进核心梳理

#### 配置表
```sql
-- 保全业务进核心配置
select * from def_kv where K ='pos.data.syn.check';

-- 抽取进核心数据条数限制
select * from def_kv where K ='data2core.limit';

-- 贷款与核心交互文件条数限制
select * from def_kv where K ='loan37';

-- 进核心批处理url配置
select * from def_kv where K in ('pos.posData.syn.CT','pos.posData.syn.DA','pos.posData.syn.DP','pos.posData.syn.LN','pos.posData.syn.RF','pos.posData.syn.RN','pos.posData.syn.SA','pos.posData.syn.WT')
```

#### 业务表
- 贷款表: pos_loan
- 还款表: pos_repay
- 其他保全表: pos_item

## 5. 监控系统设计要点

### 5.1 数据库表设计

#### 核心表结构
1. **config_info**（配置表）
2. **ftp_info**（FTP配置表）
3. **policy_monitor**（保单监控表）
4. **policy_monitor_detail**（保单监控明细表）

#### 重要字段说明
- **计价状态**: 0=已计价，1=未计价，2=不涉及
- **风险保费扣除状态**: 0=已扣风保，1=未扣风保，2=不涉及
- **贷款结息状态**: 0=已结息，1=未结息，2=不涉及
- **资产推送状态**: 0=已推送资产，1=未推送资产，2=无需推送资产

### 5.2 监控逻辑

#### 数据来源规则
- 阿里云保单以阿里云系统为准
- 其他保单以核心系统为准
- 销售渠道以核心记录的渠道为准

#### 状态判断规则
- **计价状态**: 应计价条数=实际计价条数为"是"，否则为"否"
- **风险保费状态**: 应扣除风险保费条数=实际扣除风险保费条数为"是"，否则为"否"
- **贷款结息状态**: 应结息条数=实际结息条数为"是"，否则为"否"

### 5.3 批处理任务

#### 主要批处理
1. **保单监控明细批处理**: 定时获取计价状态为"是"的保单，按渠道、产品读取资产文件并解析
2. **保单监控汇总批处理**: 基于明细数据生成汇总数据

#### 执行顺序
1. 贷款利息结息
2. 计价批处理
3. 赎回计价
4. 资产推送

### 5.4 预警机制

#### 预警内容
- 计价监控
- 风险保费监控
- 结息监控
- 资产推送监控（官网、官微、交易、渠道）

#### 预警格式
```
【YYYY-MM-DD日，投连保单监控结果如下】
【监控项】【成功/失败】【应处理保单XX条，未处理YY条】
```

## 6. 技术实现要点

### 6.1 环境要求
- JDK 1.8+
- StarRocks 2.0+（兼容MySQL协议）
- Maven 3.0+
- Node.js 12.0+
- npm 6.0+

### 6.2 项目结构
```
huida-monitor-platform
├── monitor-backend
│   ├── huida-admin（主项目入口）
│   ├── huida-framework（核心框架）
│   ├── huida-system（系统管理）
│   ├── huida-common（通用工具）
│   ├── huida-quartz（定时任务）
│   └── huida-generator（代码生成）
├── monitor-front
└── pom.xml
```

### 6.3 部署要点
- 需要配置StarRocks数据库连接
- 使用Maven进行后端打包
- 使用npm进行前端构建
- 支持定时任务调度（Quartz）