# 汇易达保单监控系统启动说明

## 📋 目录
- [环境要求](#环境要求)
- [快速启动](#快速启动)
- [详细启动步骤](#详细启动步骤)
- [启动脚本说明](#启动脚本说明)
- [常见问题](#常见问题)
- [访问地址](#访问地址)

## 🔧 环境要求

### 必需环境
- **Java**: JDK 8 或更高版本
- **Node.js**: 14.x 或更高版本
- **npm**: 6.x 或更高版本
- **Maven**: 3.6 或更高版本

### 数据库环境
- **StarRocks**: 3.3.11 或更高版本
- **Redis**: 5.0 或更高版本

### 端口要求
- **前端**: 80 (可配置)
- **后端**: 8080 (可配置)

## 🚀 快速启动

### Windows 环境
```bash
# 1. 检查环境
check-env.bat

# 2. 快速启动（推荐）
quick-start.bat

# 3. 或使用完整启动脚本
start-all.bat
```

### Linux/Unix 环境
```bash
# 1. 设置执行权限
chmod +x *.sh

# 2. 检查环境
./check-env.sh

# 3. 快速启动（推荐）
./quick-start.sh

# 4. 或使用完整启动脚本
./start-all.sh
```

## 📝 详细启动步骤

### 1. 环境检查
首先运行环境检查脚本，确保所有必需的环境都已正确安装：

**Windows:**
```bash
check-env.bat
```

**Linux:**
```bash
./check-env.sh
```

### 2. 数据库初始化
在首次启动前，需要初始化数据库：

```sql
-- 1. 创建数据库表结构
source sql/starrocks_init_ddl_v3311.sql;

-- 2. 插入初始化数据
source sql/starrocks_init_dml_v3311.sql;
```

### 3. 配置文件检查
确认以下配置文件中的数据库和Redis连接信息：

- `ruoyi-admin/src/main/resources/application-druid.yml` - 数据库配置
- `ruoyi-admin/src/main/resources/application.yml` - Redis配置

### 4. 启动服务

#### 方式一：快速启动（推荐新手）
**Windows:**
```bash
quick-start.bat
```

**Linux:**
```bash
./quick-start.sh
```

#### 方式二：分步启动
**Windows:**
```bash
# 启动完整管理界面
start-all.bat

# 选择对应的操作：
# [1] 启动后端服务
# [2] 启动前端服务  
# [3] 启动全部服务
```

**Linux:**
```bash
# 交互模式
./start-all.sh

# 或命令行模式
./start-all.sh start        # 启动全部
./start-all.sh stop         # 停止全部
./start-all.sh restart      # 重启全部
./start-all.sh status       # 查看状态
```

#### 方式三：手动启动
```bash
# 1. 构建后端项目
mvn clean package -Dmaven.test.skip=true

# 2. 启动后端服务
java -jar ruoyi-admin.jar

# 3. 启动前端服务（新开终端）
cd ruoyi-ui
npm install
npm run dev
```

## 📚 启动脚本说明

### 脚本文件列表

| 脚本文件 | 平台 | 功能描述 |
|---------|------|----------|
| `quick-start.bat` | Windows | 一键快速启动，自动检查环境和构建 |
| `quick-start.sh` | Linux | 一键快速启动，自动检查环境和构建 |
| `start-all.bat` | Windows | 完整启动管理脚本，提供多种操作选项 |
| `start-all.sh` | Linux | 完整启动管理脚本，支持交互和命令行模式 |
| `check-env.bat` | Windows | 环境检查脚本，检查所有必需环境 |
| `check-env.sh` | Linux | 环境检查脚本，检查所有必需环境 |
| `ry.bat` | Windows | 原始若依后端启动脚本 |
| `ry.sh` | Linux | 原始若依后端启动脚本 |

### 功能对比

| 功能 | quick-start | start-all | ry |
|------|-------------|-----------|-----|
| 环境检查 | ✅ | ❌ | ❌ |
| 自动构建 | ✅ | ✅ | ❌ |
| 前端启动 | ✅ | ✅ | ❌ |
| 后端启动 | ✅ | ✅ | ✅ |
| 服务管理 | ❌ | ✅ | ✅ |
| 状态查看 | ❌ | ✅ | ✅ |

## 🌐 访问地址

启动成功后，可以通过以下地址访问系统：

| 服务 | 地址 | 说明 |
|------|------|------|
| **前端系统** | http://localhost | 主要业务界面 |
| **后端API** | http://localhost:8080 | REST API接口 |
| **API文档** | http://localhost:8080/swagger-ui.html | Swagger接口文档 |
| **数据库监控** | http://localhost:8080/druid/ | Druid连接池监控 |

### 默认登录账号
- **用户名**: `admin`
- **密码**: `123456`

## ❓ 常见问题

### 1. 端口被占用
**问题**: 启动时提示端口8080或80被占用

**解决方案**:
```bash
# Windows - 查看端口占用
netstat -ano | findstr :8080
taskkill /f /pid <PID>

# Linux - 查看端口占用  
lsof -ti:8080
kill -9 <PID>
```

### 2. Java环境问题
**问题**: 提示找不到Java命令

**解决方案**:
- 确认已安装JDK 8或更高版本
- 检查JAVA_HOME环境变量
- 确认java命令在PATH中

### 3. Maven构建失败
**问题**: mvn命令执行失败

**解决方案**:
```bash
# 清理并重新构建
mvn clean
mvn compile
mvn package -Dmaven.test.skip=true
```

### 4. 前端依赖安装失败
**问题**: npm install 失败

**解决方案**:
```bash
# 清理缓存并重新安装
cd ruoyi-ui
rm -rf node_modules package-lock.json
npm cache clean --force
npm install
```

### 5. 数据库连接失败
**问题**: 后端启动时数据库连接失败

**解决方案**:
- 检查StarRocks服务是否启动
- 确认数据库连接配置正确
- 检查网络连接和防火墙设置
- 确认数据库用户权限

### 6. Redis连接失败
**问题**: Redis连接超时或失败

**解决方案**:
- 检查Redis服务是否启动
- 确认Redis配置信息正确
- 检查Redis密码和端口设置

## 🔄 服务管理命令

### Windows 环境
```bash
# 查看Java进程
jps -l

# 停止特定进程
taskkill /f /pid <PID>

# 查看端口占用
netstat -ano | findstr :8080
```

### Linux 环境
```bash
# 查看Java进程
ps -ef | grep java

# 停止特定进程
kill -9 <PID>

# 查看端口占用
lsof -ti:8080
netstat -tuln | grep :8080
```

## 📞 技术支持

如果遇到其他问题，请：

1. 查看日志文件：`logs/` 目录下的日志文件
2. 检查控制台输出信息
3. 确认所有环境要求都已满足
4. 联系技术支持团队

---

**祝您使用愉快！** 🎉
